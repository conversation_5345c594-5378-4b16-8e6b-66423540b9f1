<template>
  <div class="app-container pt20">
    <sb-el-table
        :table="table"
        @getList="getList"
        @handleTodo="handleTodo"
        @updateTableData="updateTableData"
        :on-ok="handleDoFun"
    >
      <template v-slot:title="{obj}">
        <span class="toDetail" @click="handleTodo(obj)">{{ obj.row.title }}</span>
      </template>
    </sb-el-table>

    <!-- 工单详情 -->
    <el-dialog :title="dialogTitle" :visible.sync="viewD" v-dialogDrag :close-on-click-modal="false" append-to-body :fullscreen="true">
      <work-order :key="cKey" :gps="gps" :dialogClose="dialogClose"></work-order>
    </el-dialog>
  </div>
</template>
<script>
import WorkOrder from "@/components/WorkOrder";
import {findProcessTask} from "@/api/process";

export default {
  name: "processTask",
  components: {WorkOrder},
  data() {
    return {
      viewD: false,
      dialogTitle: "",

      gps: {
        type: "task",
        location: "",
        pmInsType: "",
      },

      cKey: 0,
      table: {
        modulName: "processTask-待办列表", // 列表中文名称
        border: true, // 是否带纵向边框
        loading: false, // 加载中动画
        stripe: true, // 是否为斑马条样式
        hasSelect: false, // 是否有复选框
        showIndex: true, // 序号
        data: [], // 数据
        addAndUpdateType: "dialog",
        total: null,
        hasQueryForm: false, // 是否有查询条件
        queryForm: {
          inline: true,
          labelWidth: "80px",
          formItemList: [
            // {label: "工单编号", key: "pmInsId", type: "input"},
            // {label: "工单标题", key: "title", type: "input"},
            // {label: "流程类型",key: "pmInstType",type: "select",dictType: "processType"}
          ],
        },
        tr: [
          // {id: "pmInsId", label: "工单编号", prop: "pmInsId", width: 180},
          {id: "title", label: "工单标题", prop: "title", show: 'template', template: 'title'},
          {id: "createTrueName", label: "上一环节办理人", prop: "createTrueName", width: 120},
          {id: "startTime", label: "办理时间", prop: "startTime", width: 150},
          {id: "activityDefName", label: "当前办理环节", prop: "activityDefName", width: 130},
          // {id: "taskTrueName", label: "当前办理人", prop: "taskTrueName", width: 90},
        ],
        // hasSetup:true,
        // setup:[],
        processType: [],
        multipleSelection: [], //多选选中数据存放变量
        dialogVisible: false, //默认对话框关闭
        form: {
          width: "600px",
          labelWidth: "100px",
          inline: true,
          formItemList: [],
        },
        listFormModul: {},
        hasOperation: true, //是否有操作列表
        operation: {
          width: "80",
          fixed: "right",
          data: [
            {id: "handleTodo", name: "办理", fun: "handleTodo"},
          ],
        },
        hasPagination: true,
        listQuery: {size: 10, page: 1},
        hasBatchOperate: false, //有无批量操作
        batchOperate: {},
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 查询列表
    getList(listQuery) {
      this.table.loading = true;
      findProcessTask(listQuery || this.table.listQuery).then((res) => {
        this.table.loading = false;
        this.table.data = res.data.content;
        this.table.total = res.data.totalElements;
      }).catch((err) => {
        this.table.loading = false;
      });
    },
    // 办理
    handleTodo(obj) {
      // 参数
      this.gps = {
        type: "task",
        location: obj.row.activityDefId,
        pmInsType: obj.row.pmInsType,
        pmInsId: obj.row.pmInsId,
        taskId: obj.row.taskId,
        processInstId: obj.row.processInstId,
        processDefinitionId: obj.row.processDefId,
        processDefKey: obj.row.processDefId,
        applyNumber: obj.row.applyNumber,
        operationStatus: 1
      };

      // 工单标题
      var th = this.util.appNameTH(obj.row.pmInsType);
      this.dialogTitle = '2025年员工本人及亲属经商办企业申报';

      this.cKey++;
      this.viewD = true;
    },

    // 关闭弹框
    dialogClose() {
      this.viewD = false;
      this.getList();
    },

    // 刷新数据
    updateTableData(obj) {
      for (let i in obj) {
        this.$set(this.table, i, obj[i]);
      }
    },

    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n = this[obj[fun]].call(this, obj, data);
      return n;
    }
  }
};
</script>
<style scoped>
/* 修改公共样式弹框样式 */
::v-deep .el-dialog__header {
  text-align: center !important;
  background: white !important;
  color: black;
  font-size: 14px;
  font-weight: bold;
  border-bottom: 1px solid #f2f2f2 !important;
}

::v-deep .el-dialog__title {
  color: black !important;
  font-size: 15.5px;
}

::v-deep .el-dialog__headerbtn .el-dialog__close {
  color: black;
}
</style>