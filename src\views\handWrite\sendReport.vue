<template>
  <div :class="gps.location ? 'w99' : 'p10'">
    <div class="pageInfo" v-if="!gps.action">
			<span v-show="nextBtn" class="btn nextBtn" @click="handleNextBtn()"><svg-icon icon-class="random"></svg-icon>
				<font>{{
            gps.location == 'fillIn' || gps.location == 'coOrganizer' || gps.location == 'host' ? '提交' : gps.location == 'query' ? '归档' : '派发'
          }}</font>
			</span>
      <span v-show="gps.location" class="btn flowTrack" @click="handleFlowTrack()">
        <svg-icon icon-class="liuchengtu"></svg-icon>
        <font>流程跟踪</font>
      </span>
      <!--      <span v-show="processImg" class="btn processImg" @click="handleProcessImg()"><svg-icon icon-class="liuchengtu"></svg-icon>-->
      <!--				<font>流程图</font>-->
      <!--			</span>-->
      <span v-show="gps.location" class="btn optClose" @click="handleOptClose()"><svg-icon icon-class="close"></svg-icon>
				<font>关闭</font>
			</span>
      <span v-show="(gps.location == 'fillIn' && appFormValue.type == 1) ||  gps.location == 'host'"
            class="btn optClose" @click="handleExpert()">
        <svg-icon icon-class="baocun"></svg-icon>
				<font>导出</font>
			</span>
    </div>

    <!-- 业务表单 -->
    <div class="message tableForm">
      <div class="orderTitle" style="">豫起奋发 担当作为</div>
      <sb-el-form ref="appForm" :form="appForm" v-model="appFormValue" :disabled="appForm.formDisabled" :on-ok="handleDoFun">
      </sb-el-form>
    </div>
    <el-table class="tableCustom" :data="dataList" style="width: 100%;" border :span-method="arraySpanMethod" :cell-style="{background: '#ffffff'}"
              @selection-change="handleSelectionChange" :header-cell-class-name="cellClass">
      <el-table-column
          v-if="gps.location == 'fillIn'||gps.location == 'coOrganizer' || gps.location == 'host'"
          type="selection"
          width="40"
          align="center"
          :selectable="() => gps.type == 'task'"
      ></el-table-column>
      <el-table-column prop="lineType" label="条线" width="60" align="center">
      </el-table-column>
      <el-table-column label="目标">
        <template v-slot:default="scope">
          <span style="font-weight: bold" v-html="scope.row.targetIntor"></span>
          <br/>
          <span v-html="scope.row.targetInfo"></span>
        </template>
      </el-table-column>
      <el-table-column label="时间要求" width="110" align="center">
        <template v-slot:default="scope">
          <span v-html="scope.row.finishDataStr"></span>
        </template>
      </el-table-column>
      <!-- 通报起草 通报派发 通报归档-->
      <el-table-column v-if="gps.location != 'fillIn'&&gps.location != 'coOrganizer' && gps.location != 'host'" prop="taskHostUnitNameArray" label="涉及部门"
                       width="200" align="center">
      </el-table-column>
      <!-- 通报起草-->
      <el-table-column v-if="!gps.location || gps.location == 'zlwgjs.start'" prop="hostUnitNameTrueName" label="牵头部门接口人" width="160" align="center">
      </el-table-column>
      <!--通报派发-->
      <el-table-column v-if="gps.location == 'distribute'" prop="taskHostUnitTrueNameArray" label="部门接口人" width="160" align="center">
      </el-table-column>
      <!--通报填写 我协办的-->
      <template v-if="(gps.location == 'fillIn' || gps.location == 'coOrganizer') && appFormValue.type == 0">
        <el-table-column prop="nextTaskReportTime" label="上次填报时间" width="160" align="center">
        </el-table-column>
        <el-table-column prop="taskReportTrueName" label="填报人" width="80" align="center">
          <template v-slot:default="scope">
            <span>{{ scope.row.taskReportTrueName || user.truename }}</span>
          </template>
        </el-table-column>
        <el-table-column label="本期通报内容" width="400" align="center">
          <template v-slot:default="scope">
            <div class="w100 f-1">
              <el-input type="textarea" v-model="scope.row.taskReportContent" :autosize="{ minRows: 2, maxRows: 6}" @blur="inputBlur(scope.row)"
                        :maxlength="scope.row.wordLimit || 500" :disabled="gps.type != 'task'"></el-input>
              <span class="f-2" v-if="gps.type == 'task'">
                最多输入 <i class="red">{{ scope.row.wordLimit || 500 }}</i> 字，您已输入 <i class="red">{{ scope.row.taskReportContent?.length || 0 }}</i> 字
              </span>
            </div>
          </template>
        </el-table-column>
      </template>
      <!--通报填写 我主办的-->
      <template v-if="(gps.location == 'fillIn' || gps.location == 'host') && appFormValue.type == 1">
        <el-table-column prop="type" label="目标类型" width="80" align="center">
          <template v-slot:default="scope">
            <span>{{ scope.row.type == 1 ? '我牵头的' : '我协办的' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="column5" label="涉及部门" width="190" align="center">
          <template v-slot:default="scope">
            <div v-if="scope.row.type == 1">
              <span>{{ scope.row.taskHostUnitName }}</span>
              <span v-if="scope.row.taskHostUnitCode != user.belongDepartmentCode && gps.type == 'task'" class="red" @click="handleBack(scope.row)">
                【{{ scope.row.taskState == 1 ? '退回' : scope.row.taskState == 2 ? '已退回' : '未反馈' }}】
              </span>
            </div>
            <span v-else>{{ scope.row.taskHostUnitNameArray }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="column6" label="各部门（中心）反馈内容" width="200" align="center">
          <template v-slot:default="scope">
            <div class="w100 f-1" v-if="scope.row.type == 0 || (scope.row.type == 1 && scope.row.taskHostUnitCode == user.belongDepartmentCode)">
              <el-input type="textarea" v-model="scope.row.taskReportContent" :autosize="{ minRows: 2, maxRows: 6}" @blur="inputBlur(scope.row)"
                        :maxlength="scope.row.wordLimit || 500" :disabled="gps.type != 'task'"></el-input>
              <span class="f-2" v-if="gps.type == 'task'">
                最多输入 <i class="red">{{ scope.row.wordLimit || 500 }}</i> 字，您已输入 <i class="red">{{ scope.row.taskReportContent?.length || 0 }}</i> 字
              </span>
            </div>
            <div v-else class="txtl">
              {{ scope.row.taskState == 1 ? scope.row.taskReportContent : '' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="targetReportContent" label="本期通报内容" width="200" align="center" class-name="reportContent">
          <template v-slot:default="scope">
            <div class="w100 f-1" v-if="scope.row.type == 1">
              <el-input type="textarea" v-model="scope.row.reportContent" :autosize="{ minRows: 2, maxRows: 6}" @blur="inputBlur(scope.row)"
                        :maxlength="scope.row.wordLimit || 500" :disabled="gps.type != 'task'"></el-input>
              <span class="f-2" v-if="gps.type == 'task'">
                最多输入 <i class="red">{{ scope.row.wordLimit || 500 }}</i> 字，您已输入 <i class="red">{{ scope.row.reportContent?.length || 0 }}</i> 字
              </span>
            </div>
            <div v-else class="center f-3" style="height: 100%"></div>
          </template>
        </el-table-column>
      </template>
      <!--通报查询-->
      <template v-if="gps.location == 'query'">
        <el-table-column label="牵头部门" width="190" align="center">
          <template v-slot:default="scope">
            <div>
              <span>{{ scope.row.hostUnitName }}</span>
              <span class="red" @click="handleBack(scope.row)" v-if="gps.type == 'task'">
                【{{ scope.row.state == 1 ? '退回' : scope.row.state == 2 ? '已退回' : '未反馈' }}】
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="nextReportTime" label="上次填报时间" width="160" align="center">
        </el-table-column>
        <el-table-column prop="reportContent" label="本期通报内容" width="260" align="center">
          <template v-slot:default="scope">
            <div class="txtl">{{ scope.row.state == 1 ? scope.row.reportContent : '' }}</div>
          </template>
        </el-table-column>
      </template>

      <el-table-column label="历史填报内容" width="100" align="center"
                       v-if="gps.location == 'fillIn' || gps.location == 'query' || gps.location == 'coOrganizer' || gps.location == 'host'">
        <template v-slot="scope">
          <div class="inlineC">
            <el-button size="mini" type="primary" @click.stop="lookHistory(scope.row)">【查看】</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="退回意见" width="100" align="center" v-if="gps.location == 'coOrganizer' || gps.location == 'host'">
        <template v-slot="scope">
          <div class="inlineC">
            <el-button size="mini" type="primary" @click.stop="lookBack(scope.row)">【查看】</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 流程图 -->
    <el-dialog title="流程图" :visible.sync="diagramD" v-dialogDrag :close-on-click-modal="false" append-to-body width="1000px">
      <process-diagram :key="diagramKey" :gps="gps"></process-diagram>
    </el-dialog>

    <!-- 历史填报内容查询 -->
    <el-dialog title="历史填报内容查询" :visible.sync="hisDialog" v-dialogDrag :close-on-click-modal="false" append-to-body top="10vh" width="50%">
      <history-list v-if="hisDialog" :targetId="targetId" :gps="gps" :type="appFormValue.type" :taskHostUnitCode="taskHostUnitCode"></history-list>
    </el-dialog>

    <!-- 流程跟踪 -->
    <el-dialog title="流程跟踪" :visible.sync="trackD" v-dialogDrag :close-on-click-modal="false" append-to-body width="1200px">
      <process-track :key="trackKey" :gps="gps"></process-track>
    </el-dialog>

    <!-- 退回 -->
    <el-dialog title="填写退回意见" :visible.sync="backD" v-dialogDrag :close-on-click-modal="false" append-to-body width="600px">
      <sb-el-form ref="backForm" :form="backForm" v-model="backFormValue" :disabled="backForm.formDisabled" :on-ok="handleDoFun">
      </sb-el-form>
      <span slot="footer" class="dialog-footer">
				<el-button @click="backD = false" size="small">关闭</el-button>
				<el-button type="primary" @click="handleConfirm()" size="small">确认</el-button>
			</span>
    </el-dialog>

    <!-- 退回意见 -->
    <el-dialog title="查看退回意见" :visible.sync="backlookD" v-dialogDrag :close-on-click-modal="false" append-to-body width="1000px">
      <div class="w100" style="padding-bottom: 20px;">
        <el-table :data="backList" style="width: 100%;" border :cell-style="{background: '#ffffff'}">
          <el-table-column
              type="index"
              width="60"
              label="序号"
              align="center"
          ></el-table-column>
          <el-table-column prop="returnTime" label="退回时间" width="160" align="center">
          </el-table-column>
          <el-table-column prop="content" label="退回意见" align="center">
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  backlist,
  daochu,
  queryReportInfo, reportBack, reportDaochu, reportDetail, reportNext
} from "@/api/process";
import {mapGetters} from "vuex";

let defaultAppFormValue = {
  pmInsId: "",
  id: "",
  blank: "blank",
  applyNumber: '',
  title: ''
};

import ProcessNext from '@/components/Process/ProcessNext.vue'
import ProcessDiagram from "@/components/Process/ProcessDiagram";
import chooseUser from "@/components/chooseUser";
import ProcessTrack from "@/components/Process/ProcessTrack.vue";

export default {
  name: "application",
  props: {
    href: {
      type: Object,
      default() {
        return {};
      }
    },
    showBtn: {
      type: Object,
      // default(){
      // 	return {};
      // }
    },
    // 流程跟踪
    doFlowTrack: {
      type: Function
    },
    // 查看意见
    doViewComments: {
      type: Function
    },
    // 关闭
    dialogClose: {
      type: Function
    },
    types: {
      type: String,
      default: ''
    },
    rowData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  components: {
    ProcessTrack,
    ProcessNext,
    ProcessDiagram,
    chooseUser,
  },
  data() {
    return {
      gps: this.href,
      pmInsId: '',
      orderId: '', // 保存草稿 重置功能用

      trackD: false,
      trackKey: 0,

      processD: false,
      pnKey: 0,
      clickFlag: true, //防止多次点击

      nowTime: this.util.getNow("yyyy-MM-dd hh:mm:ss"),

      dataList: [],
      spanArr: [],//二维数组，用于存放单元格合并规则
      position: 0,//用于存储相同项的开始index

      showUserDialog: false,
      itemUser: {key: 0, nodeKey: 'id', mulitple: true, type: '3'}, // type 1 责任人 2 责任主管 3 经办人
      cd: [],

      // 业务表单
      initValue: {},
      appFormValue: Object.assign({}, defaultAppFormValue),
      appForm: {
        formDisabled: false,
        labelWidth: "140px",
        inline: true,
        formItemList: [
          {class: "c8", label: "工单标题", key: "title", type: "input", rule: {required: true}},
          {class: "c4", label: "工单编号", key: "applyNumber", type: "input", placeholder: '工单编号由系统生成', disabled: true},
          {class: "c4", label: "发起人", key: "applyTrueName", type: "input", disabled: true},
          {class: "c4", label: "发起部门", key: "belongDepartmentName", type: "input", disabled: true},
          {
            class: "c4",
            label: "截止时间",
            key: "deadline",
            type: "date",
            subtype: "datetime",
            valueFormat: "yyyy-MM-dd HH:mm",
            rule: {required: true},
            disabledDate: 'afterDate'
          },
          {class: "c12", label: "要求描述", key: "describe", type: "input", inputType: 'textarea'},
        ],
      },

      formData: {},

      // 流程图
      diagramKey: 0,
      diagramD: false,

      uploadObj: {showFileList: false, mulitple: false, limit: 10},
      selectList: [],
      hisDialog: false,

      targetId: '',
      backD: false,
      backFormValue: Object.assign({}),
      backForm: {
        formDisabled: false,
        labelWidth: "100px",
        inline: true,
        formItemList: [
          {class: "c12", label: "退回意见", key: "content", type: "input", inputType: 'textarea', rule: {required: true}, autosize: {minRows: 5}},
        ],
      },
      taskHostUnitCode: '',
      nextUser: '',
      backlookD: false,
      backList: []
    }
  },
  computed: {
    ...mapGetters(['user']),
    processImg() {
      return !this.gps.modify && (this.gps.type == "task" || this.gps.type == "join" || this.gps.type == "toRead" || this.gps.type == "doRead")
    },
    nextBtn() {
      return (!this.gps.modify && (this.gps.type == "task" || this.gps.type == "toRead")) || !this.gps.location
    }
  },
  created() {
    var query = this.util.getQueryString();
    this.gps = Object.assign(this.gps, query);
    console.log('gps', JSON.parse(JSON.stringify(this.gps)));

    setTimeout(() => {
      let height
      if (this.gps.myFrom && this.$router.currentRoute.path == "/workOrder") {
        height = window.innerHeight - 321
      } else {
        height = window.innerHeight - 381
      }
      let dom = document.querySelector('.tableCustom .el-table__body-wrapper')
      dom.setAttribute('style', `max-height: ${height}px;`)
    }, 0)

    this.initValue = {
      applyTrueName: this.$store.getters.user.truename,
      applyUserName: this.$store.getters.user.username,
      belongCompanyName: this.$store.getters.user.belongCompanyName,
      belongDepartmentName: this.$store.getters.user.belongDepartmentName,
      applyPhone: this.$store.getters.user.preferredMobile,
      applyTime: this.nowTime
    };
    this.appFormValue = Object.assign({}, defaultAppFormValue, this.initValue);

    this.initFun(); //初始化
  },
  methods: {
    cellClass(row) {
      if ((this.gps.location == 'fillIn' || this.gps.location == 'coOrganizer' || this.gps.location == 'host') && row.columnIndex === 0) {
        return 'disable-selection'
      }
    },
    async lookBack(row) {
      let res = await backlist({
        pmInsId: this.gps.pmInsId,
        targetId: row.targetId,
        departmentCode: this.gps.location == 'host' ? row.hostUnitCode : row.taskHostUnitCode
      })
      this.backList = res.data
      this.backlookD = true
    },
    handleConfirm() {
      Promise.all([this.submitForm('backForm')])
          .then(async () => {
            await reportBack({
              location: this.gps.location,
              pmInsId: this.gps.pmInsId,
              taskId: this.gps.taskId,
              targetId: this.targetId,
              nextUser: this.nextUser,
            }, {
              content: this.backFormValue.content,
              taskHostUnitCode: this.taskHostUnitCode,
            })
            this.backD = false
            this.loadForm();
          })
          .catch(() => {
            this.$message.warning('退回意见不能为空')
          })
    },
    handleBack(row) {
      if (this.gps.type != 'task') return
      if (this.gps.location == 'query' && row.state != 1) return;
      if ((this.gps.location == 'fillIn' || this.gps.location == 'host') && row.taskState != 1) return;

      if (this.gps.location == 'query') {
        this.taskHostUnitCode = ''
        this.nextUser = row.hostUnitUserName
      } else {
        this.taskHostUnitCode = row.taskHostUnitCode
        this.nextUser = row.taskHostUnitUserName
      }
      this.targetId = row.targetId
      this.backFormValue = Object.assign({})
      this.backD = true
    },
    async handleExpert() {
      let res = await reportDaochu(this.gps.location == 'query' ? 'exportQuery' : 'exportFillIn', this.gps.pmInsId)

      if (res.data) {
        this.util.blobDownload(res.data, res.filename);
      } else {
        this.$message({
          message: '导出失败',
          type: 'warning',
          duration: 1500
        });
      }
    },
    handleSelectionChange(val) {
      let arr = val.map(item => item.targetId)
      this.selectList = this.dataList.filter(item => arr.includes(item.targetId))
    },
    inputBlur() {

    },
    lookHistory(row) {
      if ((this.gps.location == 'fillIn' || this.gps.location == 'coOrganizer') && this.appFormValue.type == 0) {
        this.taskHostUnitCode = row.taskHostUnitCode
      } else {
        this.taskHostUnitCode = 0
      }
      this.targetId = row.targetId
      this.hisDialog = true
    },
    fileDownload(id) {
      this.util.fileDownload(id)
    },
    filePreview(id) {
      this.util.fileOpen(id)
    },
    nextCallBack(data) {
      let params = {location: this.gps.location || 'zlwgjs.start', pmInsId: this.gps.pmInsId || '', taskId: this.gps.taskId || ''}
      reportNext(params, data).then(res => {
        if ((this.gps.location == 'fillIn' || this.gps.location == 'coOrganizer' || this.gps.location == 'host') && this.selectList.length < this.dataList.length && this.selectList.length > 0) {
          this.loadForm()
          return
        }
        if (this.gps.myFrom && this.$router.currentRoute.path == "/workOrder") { //单点
          window.opener = null;
          window.open("", "_self");
          window.close();
        } else {
          if (!this.gps.location) {
            this.$router.push({
              name: "processTask"
            });
          } else {
            this.dialogClose();
          }
        }
      })
    },
    rowspan(idx, prop) {
      this.spanArr[idx] = [];
      this.position = 0;
      this.dataList.forEach((item, index) => {
        if (index === 0) {
          this.spanArr[idx].push(1);
          this.position = 0;
        } else {
          if (this.dataList[index][prop] === this.dataList[index - 1][prop]) {
            this.spanArr[idx][this.position] += 1;//有相同项
            this.spanArr[idx].push(0); // 名称相同后往数组里面加一项0
          } else {
            this.spanArr[idx].push(1);//同列的前后两行单元格不相同
            this.position = index;
          }
        }
      })
    },
    // 合并行
    arraySpanMethod({row, column, rowIndex, columnIndex}) {
      let start, end
      if (this.gps.location == 'fillIn' || this.gps.location == 'coOrganizer' || this.gps.location == 'host') {
        start = 0
        end = 2
        if (this.appFormValue.type == 1) {
          end = 8
        }
      } else {
        start = 0
        end = 1
      }
      for (let i = start; i < end; i++) {
        if (columnIndex === i) {
          const _row = this.spanArr[i][rowIndex];
          const _col = _row > 0 ? 1 : 0;
          // console.log('第'+rowIndex+'行','第'+i+'列','rowspan:'+_row,'colspan:'+_col)
          return {
            rowspan: _row,
            colspan: _col
          }
        }
      }
    },
    // 流程图
    handleProcessImg() {
      this.diagramKey++;
      this.diagramD = true;
    },
    // 初始化
    async initFun() {
      // 起草
      if (!this.gps.location) {
        await this.getTableList()
      } else {
        this.loadForm();
      }
    },
    async getTableList() {
      let res = await queryReportInfo()
      this.dataList = res.data.map(item => {
        item.targetIntor = this.util.htmlDecode(item.targetIntor)
        item.targetInfo = this.util.htmlDecode(item.targetInfo)
        return {
          ...item
        }
      });
      this.rowspan(0, 'lineType');
    },
    // 获取工单详情
    loadForm() {
      var data = {
        pmInsId: this.gps.pmInsId,
        location: this.gps.location,
        taskId: this.gps.taskId,
        type: this.gps.type
      };
      reportDetail(data).then((res) => {
        Object.assign(this.appFormValue, res.data)
        this.dataList = res.data.reportInfos.map((item, index) => {
          item.targetIntor = this.util.htmlDecode(item.targetIntor)
          item.targetInfo = this.util.htmlDecode(item.targetInfo)
          // 通报填写 责任部门接口人 第五列 第六列 不合并
          if ((this.gps.location == 'fillIn' && res.data.type == 1) || this.gps.location == 'host') {
            item.column5 = item.targetInfo + index
            item.column6 = item.targetInfo + index
          }
          return {
            targetReportContent: item.targetIntor + item.targetInfo + item.reportContent,
            targetIntorAndInfo: item.targetIntor + item.targetInfo,
            ...item
          }
        });
        if (this.gps.location == 'fillIn' || this.gps.location == 'host' || this.gps.location == 'coOrganizer') {
          this.selectList = []
          this.rowspan(0, 'targetIntorAndInfo')

          this.rowspan(1, 'lineType');
          // 牵头部门接口人
          if (this.appFormValue.type == 1) {
            this.rowspan(2, 'targetIntorAndInfo')
            this.rowspan(3, 'finishDataStr')
            this.rowspan(4, 'type')
            this.rowspan(5, 'column5')
            this.rowspan(6, 'column6')
            this.rowspan(7, 'targetReportContent')
          }
        } else {
          this.rowspan(0, 'lineType');
        }
        this.appForm.formDisabled = true
      });
    },
    //封装验证函数
    submitForm(formUser) {
      return new Promise((resolve, reject) => {
        if (this.$refs[formUser]) {
          this.$refs[formUser].$children[0].validate((valid) => {
            if (valid) {
              resolve()
            } else {
              reject(new Error('错误'))
            }
          })
        } else {
          resolve()
        }
      })
    },
    // 流转下一步
    handleNextBtn() {
      Promise.all([this.submitForm('appForm')])
          .then(() => {
            this.formData = JSON.parse(JSON.stringify(this.appFormValue))

            if (this.gps.location == 'fillIn' || this.gps.location == 'coOrganizer' || this.gps.location == 'host') {
              let key
              if (this.selectList.length) {
                key = 'selectList'
              } else {
                key = 'dataList'
              }
              if (this.formData.type == 0) {
                let arr = this[key].filter(a => !a.taskReportContent)
                if (arr.length > 0) {
                  this.$message.warning('填报内容不能为空')
                  return;
                }
              } else {
                let arr = this[key].filter(a => {
                  return ((a.type == 0 || (a.type == 1 && a.taskHostUnitCode == this.user.belongDepartmentCode)) && !a.taskReportContent) || (a.type == 1 && a.taskHostUnitCode != this.user.belongDepartmentCode && a.taskState != 1)
                })
                if (arr.length > 0) {
                  this.$message.warning('各部门反馈内容不能为空')
                  return;
                }

                this[key].reduce((prev, next) => {
                  if (prev.targetId == next.targetId) {
                    next.reportContent = prev.reportContent
                  }
                  return next
                }, {})
                let arr1 = this[key].filter(a => a.type == 1 && !a.reportContent)
                if (arr1.length > 0) {
                  this.$message.warning('本期通报内容不能为空')
                  return;
                }
              }

              Object.assign(this.formData, {reportInfos: this[key]})
              this.$confirm(key == 'selectList' ? '请确认是否提交所选内容？' : '请确认是否全部提交？', '提示').then(() => {
                this.nextCallBack(this.formData)
              })

              return
            }

            if (this.gps.location == 'query') {
              let arr = this.dataList.filter(a => a.state != 1)
              if (arr.length > 0) {
                this.$message.warning('本期通报内容不能为空')
                return;
              }
            }

            this.$confirm(this.gps.location == 'query' ? '请确认是否归档？' : '请确认是否派发？', '提示').then(() => {
              this.nextCallBack(this.formData)
            })
          })
          .catch(() => {
            this.$message.warning('表单数据校验不通过')
          })
    },
    // 关闭
    handleOptClose() {
      if (this.dialogClose) {
        //待办打开
        this.dialogClose();
      } else if (this.gps.myFrom && this.$router.currentRoute.path == "/workOrder") {//单点
        //单点
        window.opener = null;
        window.open("", "_self");
        window.close();
      } else {
        // let item = this.tabnav.find(item => item.path === this.$route.path);
        // this.$store.dispatch("CloseTabnav", item).then(res => {
        //   if (item.path === this.$route.path) {
        //     const lastTag = res.slice(-1)[0];
        //     // 前一个 tab-view 页面存在，就跳；不存在就到首页
        //     if (lastTag) {
        //       this.$router.push({ path: lastTag.path });
        //     } else {
        //       this.$router.push({ path: "/mywork/processTask" });
        //     }
        //   }
        // });
      }
    },
    // 流程跟踪
    handleFlowTrack() {
      this.trackKey++;
      this.trackD = true;
    },
    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n = this[obj[fun]].call(this, obj, data);
      return n;
    }
  }

};
</script>
<style scoped>
::v-deep .tableForm .el-textarea__inner {
  min-height: 48px !important;
}

::v-deep .tableCustom .disable-selection .cell .el-checkbox__inner {
  display: none;
  position: relative;
}

::v-deep .tableCustom .disable-selection .cell:before {
  display: none;
  position: relative;
}

::v-deep .tableCustom .el-table__body-wrapper {
  overflow-y: auto;
}

::v-deep .reportContent .cell,
::v-deep .reportContent .cell .f-1,
::v-deep .reportContent .cell .el-textarea,
::v-deep .reportContent .cell .el-textarea__inner {
  height: 100% !important;
}

.f-1 {
  position: relative;
}

.f-2 {
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 99;
  font-size: 11px;
  line-height: 26px;
  width: 100%;
  background: #FFFFFF;
  text-align: right;
}

.f-3 {
  position: relative;
}

.f-3:after {
  position: absolute;
  left: 50%;
  top: 50%;
  content: '';
  width: 50%;
  height: 0;
  border-bottom: 1px solid #000;
  transform: translate(-50%, -50%);
}

::v-deep .el-input__count {
  background: rgba(247, 234, 233, 0);
}

.red {
  color: rgba(192, 0, 0, 1);
  cursor: pointer;
}

::v-deep .tableCustom .el-textarea__inner {
  padding: 0;
  border: none;
  min-height: 80px !important;
  padding-bottom: 26px;
  background: #FFFFFF !important;
  color: #606266 !important;
  font-size: 13px;
}

::v-deep .tableCustom .cell {
  white-space: normal;
  overflow: visible;
  text-overflow: inherit;
}

::v-deep .el-table .cell > .inlineC > .el-button {
  background: transparent;
  border: none;
  margin: 0;
  font-size: 14px;
  color: rgba(192, 0, 0, 1);
  padding: 7px 0px;
  white-space: normal;
}

::v-deep .el-input-group__append {
  background-color: #fff3f3;
  border-color: #fff3f3;
}

::v-deep .upload_D {
  min-width: 100%;
}

::v-deep .upload_Btn .uploadB {
  right: 0;
  left: auto;
}

.m-title {
  width: 100%;
  padding-left: 10px;
  border-left: 4px solid rgba(192, 0, 0, 1);
  padding: 10px;
  font-size: 16px;
  color: #333;
  font-weight: 700;
  line-height: 30px;
  border-bottom: 1px solid #ebebeb;
  border-right: 1px solid #ebebeb;
}

.m-4 {
  height: 32px;
  display: flex;
  align-items: center;
  padding: 0;
  border-right: 1px solid #ebebeb;
  border-bottom: 1px solid #ebebeb;
}

.m-4:first-child {
  border-top: 1px solid #ebebeb;
}

::v-deep .m-4 .el-input__inner {
  height: 30px;
  line-height: 30px;
  border: none;
}

::v-deep .m-4 .el-input__icon {
  line-height: 30px;
}

.m-2 {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  width: 100px;
  height: 100%;
  background: #DDF1FE;
  padding: 0 10px;
  font-size: 14px;
  color: #606266;
  font-weight: bold;
}

.m-5 {
  flex: 1;
}
</style>