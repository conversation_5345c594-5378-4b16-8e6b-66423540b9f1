<template>
  <el-table
      v-loading.fullscreen.lock="tableLoading"
      element-loading-text="请稍后，正在查询..."
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(255, 255, 255, 0.5)" class="tableCustom" :data="dataList" style="width: 100%;"
      border :cell-style="{background: '#ffffff'}">
    <el-table-column type="index" label="序号" width="40" align="center">
    </el-table-column>
    <el-table-column prop="companyNameHn" label="所属单位" min-width="70" align="center">
    </el-table-column>
    <el-table-column prop="userCode" label="员工编号" min-width="70" align="center">
    </el-table-column>
    <el-table-column prop="applyTrueName" label="姓名" min-width="65" align="center">
    </el-table-column>
    <el-table-column prop="applyPhone" label="电话" min-width="70" align="center">
    </el-table-column>
    <el-table-column prop="companyAndPosition" label="单位及职务" min-width="70" align="center">
    </el-table-column>
    <el-table-column label="本人及相关亲属（配偶、子女及其配偶）违规持有供应商或渠道商股权情况" align="center" v-if="type=='a' || !type">
      <el-table-column label="与本人关系" min-width="70" align="center">
        <template v-slot:default="scope">
          {{ scope.row.atypeRelationName == '无' ? '' : getSelfRelation(scope.row.relationType) }}
        </template>
      </el-table-column>
      <el-table-column prop="atypeRelationName" label="相关亲属姓名" min-width="70" align="center">
        <template v-slot:default="scope">
          {{scope.row.relationType == 1 && scope.row.atypeRelationName != '无' ? '' : scope.row.atypeRelationName}}
        </template>
      </el-table-column>
      <el-table-column prop="atypeCompanyAndPosition" label="所办企业名称及所任职务" min-width="70" align="center">
      </el-table-column>
      <el-table-column prop="atypeRelationCompany" label="有业务往来的本企业内部企业名称" min-width="70" align="center">
      </el-table-column>
      <el-table-column prop="atypeBusinessDetail" label="业务往来具体情况" min-width="70" align="center">
      </el-table-column>
      <el-table-column prop="atypeBusinessTotal" label="涉及总额（万元）" min-width="70" align="center">
      </el-table-column>
    </el-table-column>
    <el-table-column label="本人及相关亲属（配偶、子女及其配偶）创办、参股或实际控制的企业，与中国移动及关联企业发生业务往来情况" align="center" v-if="type=='b' || !type">
      <el-table-column label="与本人关系" min-width="70" align="center">
        <template v-slot:default="scope">
          {{ scope.row.btypeRelationName == '无' ? '' : getSelfRelation(scope.row.relationType) }}
        </template>
      </el-table-column>
      <el-table-column prop="btypeRelationName" label="相关亲属姓名" min-width="70" align="center">
        <template v-slot:default="scope">
          {{scope.row.relationType == 1 && scope.row.btypeRelationName != '无' ? '' : scope.row.btypeRelationName}}
        </template>
      </el-table-column>
      <el-table-column prop="btypeCompanyAndPosition" label="所办企业名称及所任职务" min-width="70" align="center">
      </el-table-column>
      <el-table-column prop="btypeRelationCompany" label="有业务往来的本企业内部企业名称" min-width="70" align="center">
      </el-table-column>
      <el-table-column prop="btypeBusinessDetail" label="业务往来具体情况" min-width="70" align="center">
      </el-table-column>
      <el-table-column prop="btypeBusinessTotal" label="涉及总额（万元）" min-width="70" align="center">
      </el-table-column>
    </el-table-column>
    <el-table-column label="本人及相关亲属（配偶、子女及其配偶）在与中国移动及关联企业有业务关系的非国有企业中，负责移动合作业务情况" align="center" v-if="type=='c' || !type">
      <el-table-column label="与本人关系" min-width="70" align="center">
        <template v-slot:default="scope">
          {{ scope.row.ctypeRelationName == '无' ? '' : getSelfRelation(scope.row.relationType) }}
        </template>
      </el-table-column>
      <el-table-column prop="ctypeRelationName" label="相关亲属姓名" min-width="70" align="center">
        <template v-slot:default="scope">
          {{scope.row.relationType == 1 && scope.row.ctypeRelationName != '无' ? '' : scope.row.ctypeRelationName}}
        </template>
      </el-table-column>
      <el-table-column prop="ctypeCompanyAndPosition" label="所办企业名称及所任职务" min-width="70" align="center">
      </el-table-column>
      <el-table-column prop="ctypeRelationCompany" label="有业务往来的本企业内部企业名称" min-width="70" align="center">
      </el-table-column>
      <el-table-column prop="ctypeBusinessDetail" label="业务往来具体情况" min-width="70" align="center">
      </el-table-column>
      <el-table-column prop="ctypeBusinessTotal" label="涉及总额（万元）" min-width="70" align="center">
      </el-table-column>
    </el-table-column>
    <el-table-column label="整改落实情况">
      <el-table-column prop="reformDetail" label="已完成整改情况" min-width="70" align="center">
      </el-table-column>
      <el-table-column prop="noReformDetail" label="未完成整改情况" min-width="70" align="center">
      </el-table-column>
    </el-table-column>
    <el-table-column prop="remark" label="备注" min-width="70" align="center">
    </el-table-column>
  </el-table>
</template>
<script>

let selfRelation = [
  {name: '本人', value: '1'},
  {name: '妻子', value: '2'},
  {name: '丈夫', value: '3'},
  {name: '儿子', value: '4'},
  {name: '女儿', value: '5'},
  {name: '儿媳', value: '6'},
  {name: '女婿', value: '7'},
]

export default {
  props: {
    list: {
      type: Array,
      default: []
    },
    isHeight: {
      type: Boolean,
      default: false
    },
    type:{}
  },
  data() {
    return {

      dataList: [],
      spanArr: [],//二维数组，用于存放单元格合并规则
      position: 0,//用于存储相同项的开始index

      tableLoading: false
    }
  },
  created() {
    this.getList()
    if (this.isHeight) {
      setTimeout(() => {
        let height = window.innerHeight - 390
        let dom = document.querySelector('.tableCustom .el-table__body-wrapper')
        dom.setAttribute('style', `max-height: ${height}px;overflow: auto;`)
      }, 0)
    }

  },
  methods: {
    getSelfRelation(type) {
      // return selfRelation.filter(item => item.value == type)?.[0].name
    },
    rowspan(idx, prop) {
      this.spanArr[idx] = [];
      this.position = 0;
      this.dataList.forEach((item, index) => {
        if (index === 0) {
          this.spanArr[idx].push(1);
          this.position = 0;
        } else {
          if (this.dataList[index][prop] === this.dataList[index - 1][prop]) {
            this.spanArr[idx][this.position] += 1;//有相同项
            this.spanArr[idx].push(0); // 名称相同后往数组里面加一项0
          } else {
            this.spanArr[idx].push(1);//同列的前后两行单元格不相同
            this.position = index;
          }
        }
      })
    },
    // 合并行
    arraySpanMethod({row, column, rowIndex, columnIndex}) {
      for (let i = 1; i < 6; i++) {
        if (columnIndex === i) {
          const _row = this.spanArr[i][rowIndex];
          const _col = _row > 0 ? 1 : 0;
          // console.log('第'+rowIndex+'行','第'+i+'列','rowspan:'+_row,'colspan:'+_col)
          return {
            rowspan: _row,
            colspan: _col
          }
        }
      }
    },
    async getList() {
      this.tableLoading = true
      this.dataList = this.list

      // this.rowspan(1, 'belongCompanyName');
      // this.rowspan(2, 'employNumber');
      // this.rowspan(3, 'applyTrueName');
      // this.rowspan(4, 'applyPhone');
      // this.rowspan(5, 'companyAndPosition');
      this.tableLoading = false
    },
    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n = this[obj[fun]].call(this, obj, data);
      return n;
    }
  }

};
</script>
<style scoped>
::v-deep .el-table__header-wrapper {
  height: auto;
}

.f-1 {
  position: relative;
}

.f-2 {
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 99;
  font-size: 12px;
  line-height: 26px;
  background: #fff;
  width: 100%;
  text-align: right;
}

.f-3 {
  text-decoration: underline;
  cursor: pointer;
}

::v-deep .tableCustom .f-4 span {
  white-space: normal;
  overflow: visible;
  text-overflow: inherit;
  width: 100%;
}

.red {
  color: rgba(192, 0, 0, 1);
  cursor: pointer;
}

::v-deep .tableCustom .cell {
  white-space: normal;
  overflow: visible;
  text-overflow: inherit;
}

::v-deep .el-table .cell > .inlineC > .el-button {
  background: transparent;
  border: none;
  margin: 0;
  font-size: 14px;
  color: rgba(192, 0, 0, 1);
  padding: 7px 0px;
  white-space: normal;
}
</style>