<template>
  <div :class="'p10'">
    <div class="flex" style="margin-top: 8px;align-items: flex-start">
      <sb-el-form
          style="width: calc(100% - 150px);margin-right: 10px"
          :form="queryForm"
          v-model="listQuery"
          :from="true"
      ></sb-el-form>
      <el-button type="primary" size="small" @click="getList()">查询</el-button>
      <el-button type="primary" size="small" @click="getRest()">重置</el-button>
    </div>
    <!-- 新建的列表组件 -->
    <custom-tableMy :key="tableKey" :list="dataList"   :type="listQuery.type"     :isHeight="true"></custom-tableMy>
    <div class="pagination-container">
      <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          layout="total,sizes,prev,pager,next,jumper"
          :page-size="listQuery.size"
          :page-sizes="[10, 20, 50]"
          :hide-on-single-page="total === 0"
          :current-page="listQuery.page"
          :total="total"
          :pager-count="5"
      ></el-pagination>
    </div>

    <div style="padding: 40px 0;"></div>
  </div>
</template>
<script>
import { zgqkDetail } from "@/api/home";
import customTableMy from "@/components/customTableMy.vue";

export default {
  name: "application",
  components: {customTableMy},
  data() {
    return {
      nowTime: this.util.getNow("yyyy-MM-dd hh:mm:ss"),

      dataList: [],

      queryForm: {
        inline: true,
        labelPosition: 'right',
        labelWidth: "90px",
        formItemList: [
          {label: "单位名称",key: "belongCompanyName",type: "input"},
          {class:'c6', label: "类型", key: "type", type: "select", 
              options:[{name:'本人及相关亲属（配偶、子女及其配偶）违规持有供应商或渠道商股权情况',value:'a'}
              ,{name:'本人及相关亲属（配偶、子女及其配偶）创办、参股或实际控制的企业，与中国移动及关联企业发生业务往来情况',value:'b'}
              ,{name:'本人及相关亲属（配偶、子女及其配偶）在与中国移动及关联企业有业务关系的非国有企业中，负责移动合作业务情况',value:'c'}],},
            
        ],
      },
      listQuery: {size: 10, page: 1, belongCompanyName: '', type: 'a'},
      total: 0,
      exportId: '',
      tableLoading: false,
      tableKey: 0
    }
  },
  async created() {
    await this.getList()
  },
  methods: {
    getRest(){
      this.listQuery = {size: 10, page: 1, belongCompanyName: '', type: 'a'},
      this.getList()
    },
    handleSizeChange(val) {
      this.listQuery.size = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.listQuery.page = val;
      this.getList();
    },

    async getList() {
      this.tableLoading = true
      let list = await zgqkDetail(this.listQuery)
      this.dataList = list.data.content

      this.total = list.data.totalElements
      this.tableKey++
      this.tableLoading = false
    },
    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n = this[obj[fun]].call(this, obj, data);
      return n;
    }
  }

};
</script>
<style scoped>
::v-deep .tableCustom:before {
  height: 0;
}

::v-deep .nodata:before {
  height: 1px;
}

::v-deep .el-table__header-wrapper .el-table__cell.gutter {
  width: 20px !important;
  display: block !important;
}

::v-deep .el-input.is-disabled .el-input__inner {
  background-color: #FFFFFF;
  color: #000;
}

::v-deep .el-form-item__content {
  flex: 1;
}

::v-deep .tableCustom .f-4 span {
  white-space: normal;
  overflow: visible;
  text-overflow: inherit;
  width: 100%;
}

::v-deep .tableCustom .cell {
  white-space: normal;
  overflow: visible;
  text-overflow: inherit;
}

::v-deep .el-table .cell > .inlineC > .el-button {
  background: transparent;
  border: none;
  margin: 0;
  font-size: 14px;
  color: rgba(192, 0, 0, 1);
  padding: 7px 0px;
  white-space: normal;
}

.m-title {
  width: 100%;
  padding-left: 10px;
  border-left: 4px solid rgba(192, 0, 0, 1);
  padding: 10px;
  font-size: 16px;
  color: #333;
  font-weight: 700;
  line-height: 30px;
  border-bottom: 1px solid #ebebeb;
  border-right: 1px solid #ebebeb;
}
</style>