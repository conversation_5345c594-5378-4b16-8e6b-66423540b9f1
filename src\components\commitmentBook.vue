<template>
  <div class="w100">
    <div class="flex w100 r-top">
      <div class="center title-box">
        <svg-icon icon-class="random"></svg-icon>
        <span>最后环节</span>
      </div>
      <span class="r-title">
       须本人手写以下承诺内容，并签名。<br/>
       本人郑重承诺:<br/>
       1、我已真实完整地向组织报告了本人、亲属经商办企业情况及与中国移动及关联企业业务往来情况。<br/>
       2、我已认真学习《中国移动通信集团河南有限公司领导人员和员工及其亲属经商办企业管理规定》和《中共中国移动通信集团河南有限公司委员会关于贯彻落实中央八项规定精神进一步深化作风建设实施细则》相关文件,今后本人将严格执行该管理规定具体要求,约束本人和亲属相关行为。<br/>
       3、以上承诺若有不实,本人愿意接受组织处分。
      </span>
    </div>
    <div class="r-middle">示例：</div>
    <div class="w100 r-img">
      <img src="@/assets/images/chengnuoshu.png" class="w100">
      <span>请参考示例在空白纸手抄承诺内容签字后拍照，导入电脑后上传。</span>
    </div>
    <sb-el-form ref="appForm" :form="appForm" v-model="appFormValue" :disabled="appForm.formDisabled" :on-ok="handleDoFun" @uploadFileList="uploadFileList">
    </sb-el-form>
  </div>
</template>

<script>
import {uploadProcessFiles} from "@/api/public";

export default {
  props: {
    item: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      appFormValue: Object.assign({}, this.item),
      appForm: {
        formDisabled: true,
        labelWidth: "200px",
        inline: true,
        formItemList: [
          {
            class: "c12",
            label: "手写签字承诺书",
            key: "sysFileList",
            type: "sbUpload",
            btnText: "上传照片",
            fun: "uploadFileList",
            listType: "text",
            multiple: false,
            rule: {required: true},
            accept: '.png, .jpg, .jpeg'
          }
        ],
      },
    }
  },
  mounted() {

  },
  methods: {
    uploadFileList(obj) {
      uploadProcessFiles(obj.formData).then(async res => {
        obj.content.onSuccess(res, obj.content.file, []);
        this.$emit('uploadEmit', this.appFormValue)
      }).catch(error => {
        obj.content.onError();
      });
    },
    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n = this[obj[fun]].call(this, obj, data);
      return n;
    }
  }
}

</script>

<style scoped>
.r-img {
  padding: 0 10%;
  border-right: 1px solid #ebebeb;
  position: relative;
  border-bottom: 1px solid #ebebeb;
  padding-bottom: 10px;
}

.r-img span {
  position: absolute;
  bottom: 20px;
  left: 10%;
  font-size: 18px;
  font-weight: bold;
  color: #000;
}

.r-middle {
  font-size: 18px;
  color: #000;
  font-weight: bold;
  padding: 10px;
  border: 1px solid #ebebeb;
  border-left: none;
}

.title-box .icon {
  margin-right: 6px;
}

.title-box {
  height: 30px;
  padding: 5px 10px;
  font-size: 12px;
  color: #fff;
  background: rgba(192, 0, 0, 1);
  margin: 0 10px;
  border-radius: 2px;
  margin-top: 13px;
}

.r-top {
  border-right: 1px solid #ebebeb;
  padding-right: 10px;
}

.r-title {
  padding: 10px;
  font-size: 15px;
  color: #333;
  font-weight: 700;
  line-height: 20px;
  flex: 1;
  //border-bottom: 1px solid #ebebeb;
}

.r-text {
  font-size: 16px;
  color: #333;
  font-weight: 700;
  line-height: 30px;
  width: 80px;
}
</style>