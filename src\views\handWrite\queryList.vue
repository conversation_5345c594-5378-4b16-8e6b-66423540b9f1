<template>
  <div :class="'p10'">
    <div class="flex" style="margin-top: 8px;align-items: flex-start">
      <sb-el-form
          style="width: calc(100% - 150px);margin-right: 10px"
          :form="queryForm"
          v-model="listQuery"
          :from="true"
      ></sb-el-form>
      <el-button type="primary" size="small" @click="getList()">查询</el-button>
      <el-button type="primary" size="small" @click="handleDaochu()">导出Excel</el-button>
      <el-button type="primary" size="small" @click="handlePdf()">导出Pdf</el-button>
      <el-button type="primary" size="small" @click="handleCuiban()">催办</el-button>
    </div>
    <custom-table :key="tableKey" :list="dataList" :isHeight="true"></custom-table>
    <div class="pagination-container">
      <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          layout="total,sizes,prev,pager,next,jumper"
          :page-size="listQuery.size"
          :page-sizes="[10, 20, 50]"
          :hide-on-single-page="total === 0"
          :current-page="listQuery.page"
          :total="total"
          :pager-count="5"
      ></el-pagination>
    </div>
  </div>
</template>
<script>
import {
  cuiban,
  cuibanquery,
  totalexport,
  totalquery,
  getYearDict,
  totalDetailExportPDF
} from "@/api/process";
import customTable from "@/components/customTable.vue";

export default {
  name: "application",
  components: {customTable},
  data() {
    return {
      nowTime: this.util.getNow("yyyy-MM-dd hh:mm:ss"),

      dataList: [],

      queryForm: {
        inline: true,
        labelPosition: 'right',
        labelWidth: "90px",
        formItemList: [
          // {label: "条线名称", key: "lineName", type: "input", class: 'c3'},
          {label: "单位名称", key: "belongCompanyName", type: "input", class: 'c3'},
          {label: "部门名称", key: "belongDepartmentName", type: "input", class: 'c3'},
          {label: "申报年度", key: "year", type: "select", class: 'c3',options:[],},
 

        ],
      },
      listQuery: {size: 10, page: 1, lineName: '', belongCompanyName: '', belongDepartmentName: ''},
      total: 0,
      exportId: '',
      tableLoading: false,
      tableKey: 0
    }
  },
  async created() {
    this.getYear();
    await this.getList()
  },
  methods: {
    getYear(){
      getYearDict().then(res=>{
          this.queryForm.formItemList[2].options = res.data
          if(res.data.length>0){
            // this.queryForm.formItemList[2].options.unshift({name:'请选择',value:''})
 
          }
 

      })

    },
    async handleCuiban() {
      if (!this.dataList.length) {
        this.$alert('当前无需催办', '提示', {
          confirmButtonText: '我知道了'
        })
        return
      }

      let res = await cuibanquery(this.dataList[0].applyNumber)
      if (res.data > 0) {
        this.$confirm(
            `目前还有${res.data}人暂未提交，点击确认按钮进行催办。`,
            "温馨提示",
            {
              confirmButtonText: "确认"
            }
        ).then(async () => {
          await cuiban(this.dataList[0].applyNumber)
        })
      } else {
        this.$alert('当前无需催办', '提示', {
          confirmButtonText: '我知道了'
        })
      }
    },
    handleSizeChange(val) {
      this.listQuery.size = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.listQuery.page = val;
      this.getList();
    },
    async handleDaochu() {
      let res = await totalexport(this.listQuery)
      if (res.data) {
        this.util.blobDownload(res.data, res.filename);
      } else {
        this.$message({
          message: '导出失败',
          type: 'warning',
          duration: 1500
        });
      }
    },
    async handlePdf() {
      let res = await totalDetailExportPDF(this.listQuery)
      if (res.data) {
        this.util.blobDownload(res.data, res.filename);
      } else {
        this.$message({
          message: '导出失败',
          type: 'warning',
          duration: 1500
        });
      }
    },

    async getList() {
      this.tableLoading = true
      let list = await totalquery(this.listQuery)
      this.dataList = list.data.content
      this.total = list.data.totalElements
      this.tableKey++
      this.tableLoading = false
    },
    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n = this[obj[fun]].call(this, obj, data);
      return n;
    }
  }

};
</script>
<style scoped>
::v-deep .tableCustom:before {
  height: 0;
}

::v-deep .nodata:before {
  height: 1px;
}

::v-deep .el-table__header-wrapper .el-table__cell.gutter {
  width: 20px !important;
  display: block !important;
}

::v-deep .el-input.is-disabled .el-input__inner {
  background-color: #FFFFFF;
  color: #000;
}

::v-deep .el-form-item__content {
  flex: 1;
}

::v-deep .tableCustom .f-4 span {
  white-space: normal;
  overflow: visible;
  text-overflow: inherit;
  width: 100%;
}

::v-deep .tableCustom .cell {
  white-space: normal;
  overflow: visible;
  text-overflow: inherit;
}

::v-deep .el-table .cell > .inlineC > .el-button {
  background: transparent;
  border: none;
  margin: 0;
  font-size: 14px;
  color: rgba(192, 0, 0, 1);
  padding: 7px 0px;
  white-space: normal;
}

.m-title {
  width: 100%;
  padding-left: 10px;
  border-left: 4px solid rgba(192, 0, 0, 1);
  padding: 10px;
  font-size: 16px;
  color: #333;
  font-weight: 700;
  line-height: 30px;
  border-bottom: 1px solid #ebebeb;
  border-right: 1px solid #ebebeb;
}
</style>