<template>
  <div class="w100">
    <div class="flex a-c j-s w100 r-top">
      <div class="flex a-c">
        <div class="center title-box">
          <img src="@/assets/images/das.png" style="width: 18px;margin-right: 5px;">
          <span v-if="isKeyPost =='是'">申报事项{{ type == 'atype' ? 1 : type == 'btype' ? 2 : type == 'ctype' ? 3 : '' }}</span>
          <span v-if="isKeyPost =='否'">申报事项</span>

        </div>
        <span class="r-title">{{ title }}</span>
      </div>
      <div class="flex a-c">
        <span class="r-text">是否存在</span>
        <el-radio-group size="mini" @change="handleChange" v-model="isSheji">
          <el-radio label="1">是</el-radio>
          <el-radio label="0">否</el-radio>
        </el-radio-group>
        <el-button v-if="isSheji == 1" size="mini" type="primary" @click="handleAdd">新增</el-button>
      </div>
    </div>
    <el-table v-if="isSheji == 1" class="tableCustom" :data="dataList" style="width: 100%;" border :cell-style="{background: '#ffffff'}">
      <el-table-column label="序号" type="index" align="center">
      </el-table-column>
      <el-table-column label="与本人关系" width="110" align="center">
        <template v-slot:default="scope">
          <el-select placeholder="请选择" v-model="scope.row.relationType" @change="rTypeChange($event, scope.$index)">
            <el-option v-for="item in selfRelation" :key="item.value" :value="item.value" :label="item.name"></el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column label="相关亲属姓名" width="110" align="center">
        <template v-slot:default="scope">
          <el-input type="textarea" placeholder="请输入" v-model="scope.row[type+'RelationName']" :autosize="{ minRows: 2, maxRows: 999}"
                    maxlength="1000" :disabled="scope.row.relationType == 1"></el-input>
        </template>
      </el-table-column>
      <el-table-column label="所办企业名称及所任职务" width="170" align="center">
        <template v-slot:default="scope">
          <el-input type="textarea" placeholder="请输入" v-model="scope.row[type+'CompanyAndPosition']" :autosize="{ minRows: 2, maxRows: 999}"
                    maxlength="1000"></el-input>
        </template>
      </el-table-column>
      <el-table-column label="有业务往来的本企业内部企业名称" width="220" align="center">
        <template v-slot:default="scope">
          <el-input type="textarea" placeholder="请输入" v-model="scope.row[type+'RelationCompany']" :autosize="{ minRows: 2, maxRows: 999}"
                    maxlength="1000"></el-input>
        </template>
      </el-table-column>
      <el-table-column label="业务往来具体情况" align="center" min-width="200">
        <template v-slot:default="scope">
          <el-input type="textarea" placeholder="请输入" v-model="scope.row[type+'BusinessDetail']" :autosize="{ minRows: 2, maxRows: 999}"
                    maxlength="1000"></el-input>
        </template>
      </el-table-column>
      <el-table-column label="涉及总额（万元）" width="130" align="center">
        <template v-slot:default="scope">
          <el-input placeholder="请输入" v-model="scope.row[type+'BusinessTotal']" maxlength="1000" @input="numberCheck($event, scope.$index)"></el-input>
        </template>
      </el-table-column>
      <el-table-column label="整改落实情况" align="center">
        <el-table-column label="已完成整改情况" width="200" align="center">
          <template v-slot:default="scope">
            <!--            <el-select placeholder="请选择" v-model="scope.row[type+'ReformDetail']" clearable>-->
            <!--              <el-option v-for="(item, index) in reformOptions" :key="index" :value="item"-->
            <!--                         :label="item"></el-option>-->
            <!--            </el-select>-->
            <el-input type="textarea" placeholder="请输入" v-model="scope.row[type+'ReformDetail']" :autosize="{ minRows: 2, maxRows: 999}"
                      maxlength="1000"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="未完成整改情况" width="200" align="center">
          <template v-slot:default="scope">
            <el-input type="textarea" placeholder="请输入" v-model="scope.row[type+'NoReformDetail']" :autosize="{ minRows: 2, maxRows: 999}"
                      maxlength="1000"></el-input>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="备注" width="150" align="center">
        <template v-slot:default="scope">
          <el-input type="textarea" placeholder="请输入" v-model="scope.row[type+'Remark']" :autosize="{ minRows: 2, maxRows: 999}"
                    maxlength="1000"></el-input>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="80" align="center">
        <template v-slot="scope">
          <div class="inlineC">
            <el-button size="mini" type="primary" @click="handleDel(scope.$index)">【删除】</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div class="center f-2" v-if="isSheji" @click="confirmHandle">确认提交</div>
  </div>
</template>

<script>
export default {
  props: {
    title: String,
    list: {
      type: Array,
      default: []
    },
    gps: Object,
    isKeyPost:String,
    type: String // 字段type atype 本人及相关亲属（配偶、子女及其配偶）违规持有供应商或渠道商股权情况 btype 本人及相关亲属（配偶、子女及其配偶）创办、参股或实际控制的企业，与中国移动及关联企业发生业务往来情况 ctype 本人及相关亲属（配偶、子女及其配偶）在与中国移动及关联企业有业务关系的非国有企业中，负责移动合作业务情况
  },
  data() {
    return {
      isSheji: "",
      dataList: this.list,
      selfRelation: [
        {name: '本人', value: '1'},
        {name: '妻子', value: '2'},
        {name: '丈夫', value: '3'},
        {name: '儿子', value: '4'},
        {name: '女儿', value: '5'},
        {name: '儿媳', value: '6'},
        {name: '女婿', value: '7'},
      ],
      reformOptions: [
        '注销企业',
        '退出或转让股份',
        '退出股份并停止与中国移动业务往来'
      ]
    }
  },
  mounted() {
    if (this.gps.type == 'join') {
      if (this.list.length) {
        this.isSheji = '1'
      } else {
        this.isSheji = '0'
      }
    }
  },
  methods: {
    rTypeChange(val, index) {
      if (val == 1) {
        this.dataList[index][this.type+'RelationName'] = this.$store.getters.user.truename
      }
    },
    confirmHandle() {
      let flag = true

      if (this.isSheji == 1) {

        if (!this.dataList.length) {
          this.$message.warning('请新增涉及内容')
          return
        }

        let fontArr = [
          'RelationName',
          'CompanyAndPosition',
          'RelationCompany',
          'BusinessDetail',
          'BusinessTotal',
          'ReformDetail',
          'NoReformDetail',
          // 'Remark'
        ]
        let fontAndType = fontArr.map(a => this.type + a)

        fontAndType.unshift('relationType')
        this.dataList.forEach(a => {
          fontAndType.forEach(b => {
            // 这仨中情况不校验
            if (
                b.indexOf('RelationName') > -1 && a['relationType'] == 1 ||
                b.indexOf('ReformDetail') > -1 && (a[this.type + 'ReformDetail'] || a[this.type + 'NoReformDetail'])
            ) {

            } else if (!a[b]) {
              flag = false
            }
          })
        })
      }

      if (!flag) {
        this.$message.warning('请将涉及内容填写完整')
        return
      }
      let str = ''
      if(this.isKeyPost == '是'){
        str = this.type == 'atype' ? '您已完成事项1的填写申报，请您确定填写的所有信息均真实、准确、完整。点击“确认”后，进行事项2的填写申报' : this.type == 'btype' ? '您已完成事项2的填写申报，请您确定填写的所有信息均真实、准确、完整。点击“确认”后，进行事项3的填写申报' : '您已完成所有事项的填写申报，下面请您点击“确认”后进行“手写承诺书”环节'

      }else {
        str ='您已完成所有事项的填写申报，下面请您点击“确认”后进行“手写承诺书”环节'
      }

      this.$confirm(
          str,
          "温馨提示",
          {
            confirmButtonText: "确认",
            cancelButtonText: "返回修改",
            type: "warning",
          }
      ).then(() => {
        this.$emit('change')
      })
    },
    numberCheck(val, index) {
      setTimeout(() => {
        this.dataList[index][this.type + 'BusinessTotal'] = this.util.checkInput(val)
      }, 0)
    },
    handleAdd() {
      this.dataList.push({
        relationType: '',
        [this.type + 'RelationName']: '',
        [this.type + 'CompanyAndPosition']: '',
        [this.type + 'RelationCompany']: '',
        [this.type + 'BusinessDetail']: '',
        [this.type + 'BusinessTotal']: '',
        [this.type + 'ReformDetail']: '',
        [this.type + 'NoReformDetail']: '',
        [this.type + 'Remark']: '',
      })
    },
    handleDel(index) {
      this.dataList.splice(index, 1)
    },
    handleChange(val) {
      if (val == 1 && !this.dataList.length) {
        this.handleAdd()
      }
    }
  }
}

</script>

<style scoped>
.f-2 {
  height: 30px;
  padding: 5px 10px;
  font-size: 12px;
  color: #fff;
  background: rgba(192, 0, 0, 1);
  margin: 20px auto 10px;
  border-radius: 2px;
  width: 100px;
  cursor: pointer;
}

.title-box .icon {
  margin-right: 6px;
}

.title-box {
  height: 30px;
  padding: 5px 10px;
  font-size: 12px;
  color: #fff;
  background: rgba(192, 0, 0, 1);
  margin: 0 10px;
  border-radius: 2px
}

::v-deep .tableCustom .el-textarea__inner {
  overflow: hidden !important;
  padding: 0;
  font-size: 13px;
  min-height: 23px !important;
}

::v-deep .el-table__header-wrapper {
  height: auto;
}

::v-deep .el-radio-group {
  width: 150px;
  min-width: 150px;
}

::v-deep .el-radio__label {
  font-size: 16px;
  color: #333;
  font-weight: 700;
  line-height: 30px;
}

.r-top {
  border-right: 1px solid #ebebeb;
  padding-right: 10px;
}

.r-title {
  padding: 10px;
  font-size: 16px;
  color: #333;
  font-weight: 700;
  line-height: 30px;
  //border-bottom: 1px solid #ebebeb;
}

.r-text {
  font-size: 16px;
  color: #333;
  font-weight: 700;
  line-height: 30px;
  width: 80px;
}
</style>