<template>
  <div :class="gps.location ? 'w99' : 'p10'" style="padding-bottom: 20px">
    <div class="pageInfo" v-if="!gps.action">
      <span class="btn nextBtn" @click="handleNextBtn()"
        ><svg-icon icon-class="random"></svg-icon>
        <font>{{ gps.type == "join" ? "重新" : "" }}提交</font>
      </span>
      <span
        v-if="
          (gps.type == 'task' && step > 0) || (gps.type == 'join' && step > 1)
        "
        class="btn nextBtn"
        @click="backHandle"
      >
        <svg-icon icon-class="arrow-left-bold"></svg-icon>
        <font>返回上一级</font>
      </span>
      <!--      <span v-show="processImg" class="btn processImg" @click="handleProcessImg()"><svg-icon icon-class="liuchengtu"></svg-icon>-->
      <!--				<font>流程图</font>-->
      <!--			</span>-->
      <span class="btn optClose" @click="handleOptClose()"
        ><svg-icon icon-class="close"></svg-icon>
        <font>关闭</font>
      </span>
    </div>

    <!-- 业务表单 -->
    <div class="message tableForm">
      <div class="orderTitle" style="">
        中国移动河南公司2025年员工及亲属经商办企业情况申报承诺表
      </div>
      <sb-el-form
        ref="appForm"
        :form="appForm"
        v-model="appFormValue"
        :disabled="appForm.formDisabled"
        :on-ok="handleDoFun"
      >
      </sb-el-form>
      <div class="m-title flex a-c j-s" :class="{ 'm-title1': step == 4 }">
        <span v-show="step < 4">填报说明</span>
        <el-button
          v-if="step > 0"
          type="primary"
          size="medium"
          @click="
            stepNew = 1;
            step = 0;
          "
          >修改个人信息</el-button
        >
      </div>
      <div class="tip" v-show="step == 0">
        1.本次需填报本人及直系亲属基本信息，需要申报的人员范围为，本人、配偶（妻子/丈夫）、子女（儿子/女儿）、子女配偶（儿媳/女婿），填报信息包括与本人关系、姓名、手机号码、其他常用手机号码和身份证相关信息，未成年子女信息无需进行填报。<br />
        <template v-if="appFormValue.isKeyPost == '是'">
          2.以下三种行为需要申报：<br />
          ①本人及相关亲属（配偶、子女及其配偶）违规持有供应商或渠道商股权情况；<br />
          ②本人及相关亲属（配偶、子女及其配偶）创办、参股或实际控制的企业，与中国移动及关联企业发生业务往来情况；<br />
          ③本人及相关亲属（配偶、子女及其配偶）在与中国移动及关联企业有业务关系的非国有企业中，负责移动合作业务情况。<br />
        </template>
        <template v-if="appFormValue.isKeyPost == '否'">
           2.以下行为需要申报：<br />
           本人及相关亲属（配偶、子女及其配偶）创办、参股或实际控制的企业，与中国移动及关联企业发生业务往来情况；<br />
        </template>
        3.请您如实填写，若有不实，一经发现公司将严肃处理。
        <!-- 1.本次申报为所属单位关键岗位员工本人及亲属经商办企业情况。如不存在相关业务往来情况的，要进行“零申报”，在“相关亲属姓名”栏填写“无”。<br/>
        2.同一亲属与中国移动及关联企业多家有业务往来的，需要逐一列出，不得遗漏；<br/>
        3.相关亲属多人存在与中国移动及关联企业多家有业务往来的，需要逐一列出，不得遗漏；<br/>
        4.与本人关系，请按如下填写：本人/妻子/丈夫/儿子/女儿/儿媳/女婿；<br/>
        5.“业务往来情况”填写最早业务开始时间、业务往来主要内容等。<br/>
        6.“已完成整治情况”包括亲属和特定关系人终止与本企业业务往来、转让所办企业股权、退出所办企业或其他方式等。<br/>
        7.“未完成整治情况”包括未完成整治的原因、拟采取的措施等。<br/>
        8.如有疑问，请查看 <a @click="getTip" class="operate-tip">操作说明</a> 或联系维护人员，联系方式13623850647。 -->
      </div>
      <div class="tip" v-show="step > 0 && step != 4">
        1.本次需填报本人及直系亲属基本信息，需要申报的人员范围为，本人、配偶（妻子/丈夫）、子女（儿子/女儿）、子女配偶（儿媳/女婿），填报信息包括与本人关系、姓名、手机号码、其他常用手机号码和身份证相关信息，未成年子女信息无需进行填报。<br />
         <template v-if="appFormValue.isKeyPost == '是'">
          2.以下三种行为需要申报：<br />
          ①本人及相关亲属（配偶、子女及其配偶）违规持有供应商或渠道商股权情况；<br />
          ②本人及相关亲属（配偶、子女及其配偶）创办、参股或实际控制的企业，与中国移动及关联企业发生业务往来情况；<br />
          ③本人及相关亲属（配偶、子女及其配偶）在与中国移动及关联企业有业务关系的非国有企业中，负责移动合作业务情况。<br />
         </template>
          <template v-if="appFormValue.isKeyPost == '否'">
            2.以下行为需要申报：<br />
           本人及相关亲属（配偶、子女及其配偶）创办、参股或实际控制的企业，与中国移动及关联企业发生业务往来情况；<br />
          </template>
        3.“中国移动及关联企业”是指中国移动总部和所属各单位以及中国移动控股的企业；“业务往来”、“业务关系”是指承揽工程项目、提供物资供应、代理通信业务、承揽外包服务、开展合作业务、承揽中介服务等情形，不包括集团客户、个人客户等通信业务服务对象；“参股”不包括个人通过二级市场购买并持有上市公司股票、通过员工持股计划获得股票的情形；负责移动合作业务是承担与中国移动合作相关的商务谈判、招投标采购等活动。<br />
        <span style="color: red"
          >4.“业务往来情况”填写最早业务开始时间、业务往来主要内容等。</span
        ><br />
        <span style="color: red"
          >5.“已完成整治情况”包括亲属和特定关系人终止与本企业业务往来、转让所办企业股权、推出所办企业或其他方式等。</span
        ><br />
        <span style="color: red"
          >6.“未完成整治情况”包括未完成整治的原因、拟采取的措施等。</span
        ><br />
        <span style="color: red"
          >7.请您如实填写，若有不实，一经发现公司将严肃处理。</span
        >
      </div>
      <template v-if="appFormValue.id">
        <!-- <relation-first v-show="stepNew == 3" :list='childInfo.list' :infouser='infoFormValue' :spouse='peiouFormValue' :gps="gps" title=""></relation-first> -->
        <relation-table
          v-show="step == 1 && appFormValue.isKeyPost =='是'"
          key="a"
          ref="refA"
          type="atype"
          :list="appFormValue.supplierRegulationList || []"
          :gps="gps"
          :isKeyPost='appFormValue.isKeyPost'
          title="本人及相关亲属（配偶、子女及其配偶）违规持有供应商或渠道商股权情况"
          @change="stepAChange"
        ></relation-table>
        <relation-table
          v-show="step == 2"
          key="b"
          ref="refB"
          type="btype"
          :isKeyPost='appFormValue.isKeyPost'
          :list="appFormValue.businessRelationList || []"
          :gps="gps"
          title="本人及相关亲属（配偶、子女及其配偶）创办、参股或实际控制的企业，与中国移动及关联企业发生业务往来情况"
          @change="step3Change"
        ></relation-table>
        <relation-table
          v-show="step == 3 && appFormValue.isKeyPost =='是'"
          key="c"
          ref="refC"
          type="ctype"
          :isKeyPost='appFormValue.isKeyPost'
          :list="appFormValue.liabilityRegulationList || []"
          :gps="gps"
          title="本人及相关亲属（配偶、子女及其配偶）在与中国移动及关联企业有业务关系的非国有企业中，负责移动合作业务情况"
          @change="step3Change"
        ></relation-table>
        <commitment-book
          v-show="step == 4"
          @uploadEmit="uploadEmit"
          :item="{ sysFileList: appFormValue.sysFileList || [] }"
        ></commitment-book>
      </template>
      <!-- 本人信息 -->
      <div v-if="stepNew == 1">
        <div class="m-title flex a-c j-s">
          <span>本人信息</span>
        </div>
        <sb-el-form
          ref="infoForm"
          :form="infoForm"
          v-model="infoFormValue"
          :disabled="infoForm.formDisabled"
          :on-ok="handleDoFun"
        >
        </sb-el-form>
      </div>
      <!-- 配偶信息 -->
      <div v-if="stepNew == 2">
        <div class="m-title flex a-c j-s">
          <span>配偶信息填报</span>
        </div>
        <sb-el-form
          ref="peiouForm"
          :form="peiouForm"
          v-model="peiouFormValue"
          :disabled="peiouForm.formDisabled"
          :on-ok="handleDoFun"
        >
          <template v-slot:isSpouse>
            <el-radio-group
              v-model="peiouFormValue.isSpouse"
              size="mini"
              @change="changePeiou"
            >
              <el-radio :label="1">进行申报</el-radio>
              <el-radio :label="0">无需申报</el-radio>
            </el-radio-group>

            <el-select
              v-if="peiouNoteShow"
              size="mini"
              v-model="peiouFormValue.isSpouseNote"
              placeholder="--请选择--"
            >
              <el-option
                v-for="item in peiouList"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </template>
        </sb-el-form>
      </div>
      <!-- 子女信息填报 -->
      <div v-if="stepNew == 2">
        <div class="m-title flex a-c j-s">
          <span>子女信息填报</span>
        </div>
        <sb-el-form
          ref="childForm"
          :form="childForm"
          v-model="childFormValue"
          :disabled="childForm.formDisabled"
          :on-ok="handleDoFun"
        >
          <template v-slot:isChild>
            <el-radio-group
              v-model="childFormValue.isChild"
              size="mini"
              @change="changeChild"
            >
              <el-radio :label="1">进行申报</el-radio>
              <el-radio :label="0">无需申报</el-radio>
            </el-radio-group>

            <el-select
              v-if="childNoteShow"
              size="mini"
              v-model="childFormValue.isChildNote"
              placeholder="--请选择--"
            >
              <el-option
                v-for="item in childNoteList"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </template>
        </sb-el-form>
        <el-form
          :inline="true"
          v-if="childShow"
          :model="childInfo"
          ref="childInfo"
          label-position="left"
          label-width="200px"
          class="addMoney"
        >
          <div
            v-for="(domain, index) in childInfo.list"
            :key="index"
            class="all"
          >
            <div class="del-line" v-if="index != 0" @click="childDel(index)">
              删除<br />本行
            </div>
            <div>
              <div :class="{ inlineB: true, c4: true, inputdel: index != 0 }">
                <el-form-item
                  :label="'子女' + (index + 1) + '姓名'"
                  size="small"
                >
                  <el-input
                    v-model="domain.childName"
                    :placeholder="'子女' + (index + 1) + '姓名'"
                    size="minni"
                  ></el-input>
                </el-form-item>
              </div>
              <div class="inlineB c4">
                <el-form-item label="与本人关系" size="small">
                  <el-select
                    v-model="domain.childRelation"
                    placeholder="请选择"
                    size="minni"
                  >
                    <el-option
                      v-for="item in selfRelation"
                      :key="item.value"
                      :label="item.name"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </div>
              <div class="inlineB c4">
                <el-form-item label="手机号码" size="small">
                  <el-input
                    v-model="domain.childTel"
                    placeholder="请输入手机号码"
                    size="minni"
                  ></el-input>
                </el-form-item>
              </div>
              <div :class="{ inlineB: true, c8: true, inputdel: index != 0 }">
                <el-form-item label="其他手机号码" size="small">
                  <el-input
                    v-model="domain.childOtherTel"
                    class="input3"
                    placeholder="如无填写无即可，多个号码用英文逗号分隔"
                    size="minni"
                  ></el-input>
                </el-form-item>
              </div>
              <div class="inlineB c4">
                <el-form-item label="身份证号" size="small">
                  <el-input
                    v-model="domain.childIdCard"
                    placeholder="请输入身份证号"
                    size="minni"
                  ></el-input>
                </el-form-item>
              </div>
            </div>
          </div>
        </el-form>
      </div>
    </div>
    <!-- 提交按钮 -->
    <div class="flex j-c a-c mt20">
      <el-button
        type="primary"
        @click="backStep()"
        v-if="stepNew == 2"
        size="medium"
        >返回修改本人信息</el-button
      >
      <el-button
        type="primary"
        @click="handleStep()"
        v-if="stepNew >= 1"
        size="medium"
        >确认提交</el-button
      >
    </div>

    <!-- 确认信息 -->
    <el-dialog
      title="身份信息确认"
      :visible.sync="infoD"
      v-dialogDrag
      :close-on-click-modal="false"
      append-to-body
      width="900px"
      :show-close="false"
    >
      <div class="tableForm">
        <sb-el-form
          ref="infoForm"
          :form="infoForm"
          v-model="infoFormValue"
          :disabled="infoForm.formDisabled"
          :on-ok="handleDoFun"
        >
        </sb-el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <!--				<el-button @click="() => { infoD = false, handleOptClose() }" size="medium">取消</el-button>-->
        <el-button type="primary" @click="confirmInfo()" size="medium"
          >确认无误</el-button
        >
      </span>
    </el-dialog>

    <!-- 提示 -->
    <el-dialog
      title="填报说明"
      :visible.sync="tipD"
      v-dialogDrag
      :close-on-click-modal="false"
      append-to-body
      width="700px"
      :show-close="false"
    >
      <div class="tip" style="border: none">
        1.本次需填报本人及直系亲属基本信息，需要申报的人员范围为，本人、配偶（妻子/丈夫）、子女（儿子/女儿）、子女配偶（儿媳/女婿），填报信息包括与本人关系、姓名、手机号码、其他常用手机号码和身份证相关信息，未成年子女信息无需进行填报。<br />
        <template v-if="appFormValue.isKeyPost == '是'">
          2.以下三种行为需要申报：<br />
          ①本人及相关亲属（配偶、子女及其配偶）违规持有供应商或渠道商股权情况；<br />
          ②本人及相关亲属（配偶、子女及其配偶）创办、参股或实际控制的企业，与中国移动及关联企业发生业务往来情况；<br />
          ③本人及相关亲属（配偶、子女及其配偶）在与中国移动及关联企业有业务关系的非国有企业中，负责移动合作业务情况。<br />
        </template>
        <template v-if="appFormValue.isKeyPost == '否'">
          2.以下行为需要申报：<br />
          本人及相关亲属（配偶、子女及其配偶）创办、参股或实际控制的企业，与中国移动及关联企业发生业务往来情况；<br />
        </template>
        3.请您如实填写，若有不实，一经发现公司将严肃处理。
      </div>
      <el-checkbox v-model="isKnow" style="padding-left: 10px"
        >我已知晓以上填报要求，开始确认身份信息及情况填报。</el-checkbox
      >
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="startWrite()" size="medium"
          >开始填报</el-button
        >
      </span>
    </el-dialog>

    <!-- 确认提交提示 -->
    <el-dialog
      title="温馨提示"
      :visible.sync="confirmTips"
      v-dialogDrag
      :close-on-click-modal="false"
      append-to-body
      width="700px"
      :show-close="false"
    >
      <div class="tip" style="border: none; color: rgba(192, 0, 0, 1)">
        您已完成本人及相关亲属信息填报，请您确定填写的所有信息均真实、准确、完整。点击“确认”后，进行事项1的填写申报
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="backStep()" size="medium"
          >返回修改</el-button
        >
        <el-button type="primary" @click="confirmUser()" size="medium"
          >确认</el-button
        >
      </span>
    </el-dialog>

    <!-- 选择人员、部门 -->
    <el-dialog
      title="预览"
      :visible.sync="viewDialog"
      v-dialogDrag
      :close-on-click-modal="false"
      append-to-body
      :fullscreen="true"
      center
      @close="closeHandle"
    >
      <div class="f-1 flex a-c j-s">
        <span></span>
        <span>填报日期：{{ appFormValue.modifiedTime }}</span>
      </div>
      <custom-table v-if="viewDialog" :list="viewList"  :isKeyPost="appFormValue.isKeyPost"></custom-table>
      <div class="m-title">
        <span>填报说明</span>
      </div>
      <div class="tip" style="border-left: 1px solid #ebebeb">
        1.如不存在相关业务往来情况的，要进行“零申报”，在“相关亲属姓名”栏填写“无”。<br />
        2.同一亲属与中国移动及关联企业多家有业务往来的，需要逐一列出，不得遗漏；<br />
        3.相关亲属多人存在与中国移动及关联企业多家有业务往来的，需要逐一列出，不得遗漏；<br />
        4.与本人关系，请按如下填写：本人/妻子/丈夫/儿子/女儿/儿媳/女婿；<br />
        5.“业务往来情况”填写最早业务开始时间、业务往来主要内容等。<br />
        6.“已完成整治情况”包括亲属和特定关系人终止与本企业业务往来、转让所办企业股权、退出所办企业或其他方式等。<br />
        7.“未完成整治情况”包括未完成整治的原因、拟采取的措施等。
      </div>
      <div class="tableForm" style="margin-bottom: 20px; margin-top: 0">
        <sb-el-form
          v-if="gps.operationStatus == 1"
          ref="uploadForm"
          :form="uploadForm"
          v-model="appFormValue"
          :disabled="uploadForm.formDisabled"
          :on-ok="handleDoFun"
          @uploadFileList="uploadFileList"
        >
        </sb-el-form>
      </div>
      <div class="w100" v-if="appFormValue?.sysFileList" style="padding: 0 10%">
        <img
          :src="appFormValue.sysFileList[0].anonymousFilePath"
          class="w100"
        />
      </div>
      <span slot="footer" class="dialog-footer" v-if="gps.operationStatus == 1">
        <el-button @click="backUpdate" size="medium">修改填报内容</el-button>
        <el-button type="primary" @click="handleConfirm()" size="medium"
          >确认提交</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { phone, cardNo } from "@/assets/js/validate.js";
import {
  getuserinfo,
  operatetip,
  preview,
  processNext,
  updateuserinfo,
} from "@/api/process";
import { getFeedFormDetail } from "@/api/apply/application";

let defaultAppFormValue = {
  pmInsId: "",
  id: "",
  blank: "blank",
};

import ProcessNext from "@/components/Process/ProcessNext.vue";
import ProcessDiagram from "@/components/Process/ProcessDiagram";
import chooseUser from "@/components/chooseUser";
import relationTable from "@/components/relationTable.vue";
import customTable from "@/components/customTable.vue";
import commitmentBook from "@/components/commitmentBook.vue";
import relationFirst from "@/components/relationFirst.vue";
import { uploadProcessFiles } from "@/api/public";
import { readonly } from "vue";
//手机号
const isphone = (rule, value, callback) => {
  if (value != null && value != "") {
    let pwdMes = phone(value);
    if (typeof pwdMes == "string") {
      callback(new Error(pwdMes));
    } else {
      callback();
    }
  } else {
    callback();
  }
};
//身份证号
const iscardNo = (rule, value, callback) => {
  if (value != null && value != "") {
    let pwdMes = cardNo(value);
    if (typeof pwdMes == "string") {
      callback(new Error(pwdMes));
    } else {
      callback();
    }
  } else {
    callback();
  }
};

export default {
  name: "application",
  props: {
    href: {
      type: Object,
      default() {
        return {};
      },
    },
    showBtn: {
      type: Object,
      // default(){
      // 	return {};
      // }
    },
    // 流程跟踪
    doFlowTrack: {
      type: Function,
    },
    // 查看意见
    doViewComments: {
      type: Function,
    },
    // 关闭
    dialogClose: {
      type: Function,
    },
    types: {
      type: String,
      default: "",
    },
    rowData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  components: {
    ProcessNext,
    ProcessDiagram,
    chooseUser,
    relationTable,
    customTable,
    commitmentBook,
    relationFirst,
  },
  data() {
    return {
      gps: this.href,
      nowTime: this.util.getNow("yyyy-MM-dd hh:mm:ss"),
      step: 0, // 页面步骤
      // 业务表单
      initValue: {},
      appFormValue: Object.assign({}, defaultAppFormValue),
      appForm: {
        formDisabled: true,
        labelWidth: "200px",
        inline: true,
        formItemList: [
          {
            class: "c4",
            label: "所属单位",
            key: "belongCompanyName",
            type: "input",
          },
          {
            class: "c4",
            label: "填报人姓名",
            key: "applyTrueName",
            type: "input",
          },
          { class: "c4", label: "员工编号", key: "userCode", type: "input" },
          { class: "c4", label: "手机号码", key: "applyPhone", type: "input" },
          {
            class: "c4",
            label: "所属部门",
            key: "belongDepartmentName",
            type: "input",
          },
          {
            class: "c4",
            label: "是否关键职位",
            key: "isKeyPost",
            type: "input",
            show: true,
          },
          // {class: "c4", label: "工作岗位", key: "companyAndPosition", type: "input",show:false},
          {
            class: "c12",
            label: "填报日期",
            key: "modifiedTime",
            type: "input",
            placeholder: "由系统自动生成",
          },
          {
            class: "c12",
            label: "手写签字承诺书",
            key: "sysFileList",
            type: "sbUpload",
            btnText: "上传照片",
            fun: "uploadFileList",
            listType: "text",
            multiple: false,
            rule: { required: true },
            accept: '.png, .jpg, .jpeg',
            show: false,
          },
        ],
      },
      infoFormValue: Object.assign({}),
      infoForm: {
        formDisabled: false,
        labelWidth: "200px",
        inline: true,
        formItemList: [
          {
            class: "c4",
            label: "所属单位",
            key: "belongCompanyName",
            type: "input",
            rule: { required: true },
          },
          {
            class: "c4",
            label: "填报人姓名",
            key: "applyTrueName",
            type: "input",
            rule: { required: true },
          },
          {
            class: "c4",
            label: "员工编号",
            key: "userCode",
            type: "input",
            rule: { required: true },
            disabled: true,
          },
          {
            class: "c4",
            label: "手机号码",
            key: "applyPhone",
            type: "input",
            rule: { required: true, type: "phone" },
          },
          {
            class: "c4",
            label: "所属部门",
            key: "belongDepartmentName",
            type: "input",
            rule: { required: true },
          },
          {
            class: "c4",
            label: "是否关键职位",
            key: "isKeyPost",
            type: "input",
            rule: { required: true },
            disabled: true,
          },
          {
            class: "c8",
            label: "其他手机号码",
            key: "otherTel",
            type: "input",
            rule: { required: true },
            placeholder: "如无填写无即可，多个号码用英文逗号分隔",
          },
          {
            class: "c4",
            label: "身份证号",
            key: "idCard",
            type: "input",
            rule: { required: true, type: "cardNo" },
          }, //type: 'cardNo'
          // {class: "c6", label: "工作岗位", key: "companyAndPosition", type: "input", rule: {required: true}},
        ],
      },
      peiouFormValue: Object.assign({}),
      peiouForm: {
        formDisabled: false,
        labelWidth: "200px",
        inline: true,
        formItemList: [
          {
            class: "c12",
            label: "配偶信息情况",
            key: "isSpouse",
            type: "template",
            template: "isSpouse",
            rule: { required: true },
            options: [
              { name: "进行申报", value: "1" },
              { name: "无需申报", value: "0" },
            ],
            changeFun: "changePeiou",
          },
        ],
      },
      childFormValue: Object.assign({}),
      childForm: {
        formDisabled: false,
        labelWidth: "200px",
        inline: true,
        formItemList: [
          {
            class: "c12",
            label: "子女信息情况",
            key: "isChild",
            type: "template",
            template: "isChild",
            rule: { required: true },
            options: [
              { name: "进行申报", value: "1" },
              { name: "无需申报", value: "0" },
            ],
            changeFun: "changeChild",
          },
        ],
      },
      childInfo: {
        list: [
          {
            childName: "",
            childRelation: "",
            childTel: "",
            childOtherTel: "",
            childIdCard: "",
          },
          {
            childName: "",
            childRelation: "",
            childTel: "",
            childOtherTel: "",
            childIdCard: "",
          },
          {
            childName: "",
            childRelation: "",
            childTel: "",
            childOtherTel: "",
            childIdCard: "",
          },
          {
            childName: "",
            childRelation: "",
            childTel: "",
            childOtherTel: "",
            childIdCard: "",
          },
        ],
      },
      childShow: false,

      formData: {},

      viewDialog: false,
      viewList: [],

      isKnow: false,
      tipD: false,

      uploadForm: {
        formDisabled: true,
        labelWidth: "200px",
        inline: true,
        formItemList: [
          {
            class: "c12",
            label: "手写签字承诺书",
            key: "sysFileList",
            type: "sbUpload",
            btnText: "上传照片",
            fun: "uploadFileList",
            listType: "text",
            multiple: false,
            rule: { required: true },
            accept: '.png, .jpg, .jpeg'
          },
        ],
      },
      stepNew: 1,
      infoD: false,
      confirmTips: false,
      peiouList: [
        {
          value: "未婚",
          name: "未婚",
        },
        {
          value: "离异",
          name: "离异",
        },
      ],
      peiouNoteShow: false,
      childNoteList: [
        {
          value: "未育",
          name: "未育",
        },
        {
          value: "子女未成年",
          name: "子女未成年",
        },
      ],
      childNoteShow: false,
      selfRelation: [
        { name: "长子", value: "4" },
        { name: "次子", value: "8" },
        { name: "长女", value: "5" },
        { name: "次女", value: "9" },
        { name: "儿媳", value: "6" },
        { name: "女婿", value: "7" },
      ],
    };
  },
  computed: {
    processImg() {
      return (
        !this.gps.modify &&
        (this.gps.type == "task" ||
          this.gps.type == "join" ||
          this.gps.type == "toRead" ||
          this.gps.type == "doRead")
      );
    },
  },
 
  async created() {
    console.log('feedBack.vue created - 组件已创建')
    var query = this.util.getQueryString();
    this.gps = Object.assign(this.gps, query);
    console.log("gps", JSON.parse(JSON.stringify(this.gps)));

    if (this.gps.type == "task") {
      // let res = await getuserinfo(this.gps.applyNumber)
      // Object.assign(this.infoFormValue, res.data)
      // Object.assign(this.appFormValue, res.data)

      this.tipD = true;
    }

    this.initFun(); //初始化
  },
  
  methods: {
    // 处理步数
    async handleStep() {
      if (this.stepNew == 1) {
        this.$refs["infoForm"].$children[0].validate((valid) => {
          if (!valid) {
            this.$message({
              message: "本人信息表单数据校验不通过",
              type: "warning",
              duration: 1500,
            });
            return false;
          } else {
            this.appFormValue = Object.assign({}, this.infoFormValue);
            this.stepNew++;
          }
        });
      } else if (this.stepNew == 2) {
        var flag = true;
        console.log(this.peiouFormValue, this.childFormValue, "2====");

        if (
          this.peiouFormValue.isSpouse == undefined ||
          this.childFormValue.isChild == undefined
        ) {
          this.$message({
            message: "配偶信息情况和子女信息情况必填",
            type: "warning",
            duration: 1500,
          });
          flag = false;
          return false;
        }

        if (this.peiouFormValue.isSpouse == "1") {
          await this.$refs["peiouForm"].$children[0].validate((valid) => {
            if (!valid) {
              this.$message({
                message: "配偶信息表单数据校验不通过",
                type: "warning",
                duration: 1500,
              });
              flag = false;
              return false;
            }
          });
        } else {
          if (!this.peiouFormValue.isSpouseNote) {
            this.$message({
              message: "无需申报需选择原因！",
              type: "warning",
              duration: 1500,
            });
            flag = false;
            return false;
          }
        }
        if (this.childFormValue.isChild == "1") {
          console.log(this.childInfo.list, "1====");
          const childList = this.childInfo.list;
          let hasValue = false;
          // 检查是否至少有一个字段有值
          for (const item of childList) {
            if (Object.values(item).some((value) => value.trim() !== "")) {
              hasValue = true;
              break;
            }
          }
          if (!hasValue) {
            this.$message({
              message: "至少填写一条子女信息",
              type: "warning",
              duration: 1500,
            });
            flag = false;
          } else {
            // 检查有值的条目是否所有字段都填写完整
            for (const [index, item] of childList.entries()) {
              if (Object.values(item).some((value) => value.trim() !== "")) {
                if (Object.values(item).some((value) => value?.trim() === "")) {
                  this.$message({
                    message: `第 ${index + 1} 条子女信息需填写完整`,
                    type: "warning",
                    duration: 1500,
                  });
                  flag = false;
                  break;
                }

                if (item.childTel.trim() !== "") {
                  // 使用一个变量标记手机号是否有错误
                  let phoneError = false;
                  await new Promise((resolve) => {
                    isphone({}, item.childTel, (error) => {
                      if (error) {
                        this.$message({
                          message: `第 ${
                            index + 1
                          } 条子女信息的手机号格式不正确`,
                          type: "warning",
                          duration: 1500,
                        });
                        flag = false;
                        phoneError = true;
                      }
                      resolve();
                    });
                  });
                }
                // 校验身份证号
                if (item.childIdCard.trim() !== "") {
                  await new Promise((resolve) => {
                    iscardNo({}, item.childIdCard, (error) => {
                      if (error) {
                        this.$message({
                          message: `第 ${
                            index + 1
                          } 条子女信息的身份证号格式不正确`,
                          type: "warning",
                          duration: 1500,
                        });
                        flag = false;
                      }
                      resolve();
                    });
                  });
                }
              }
            }
          }
        }else {
          if (!this.childFormValue.isChildNote) {
            this.$message({
              message: "无需申报需选择原因！",
              type: "warning",
              duration: 1500,
            });
            flag = false;
            return false;
          }
        }
        if (flag) {
          this.$confirm(
            "您已完成本人及相关亲属信息填报，请您确定填写的所有信息均真实、准确、完整。点击“确认”后，进行本人及相关亲属经商办企业情况申报",
            "温馨提示",
            {
              confirmButtonText: "确认",
              cancelButtonText: "返回修改",
              // type: "warning",
            }
          )
            .then(() => {
              if(this.appFormValue.isKeyPost == '是'){
                this.step = 1;
              }else{
                this.step = 2;

              }
              this.stepNew = 0;
              this.appFormValue = Object.assign(
                {},
                this.infoFormValue,
                this.childFormValue
              );
              this.appFormValue.isSpouseNote = this.peiouFormValue.isSpouseNote;
              this.appFormValue.isSpouse = this.peiouFormValue.isSpouse;
              this.appFormValue.spouse = Object.assign({}, this.peiouFormValue);
              this.appFormValue.usChildList = JSON.parse(
                JSON.stringify(this.childInfo.list)
              );
              this.appFormValue.usChildList = JSON.parse(
                JSON.stringify(this.childInfo.list)
              ).filter((item) => {
                return Object.values(item).some(
                  (value) =>
                    value !== null && value !== undefined && value.trim() !== ""
                );
              });
            })
            .catch(() => {
              this.stepNew = 1;
            });
        }
      }
    },
    backStep() {
      this.stepNew = 1;
    },
    confirmUser() {
      this.step = 1;
      this.stepNew = 0;
      this.confirmTips = false;
    },
    // 配偶选择
    changePeiou(value) {
      var arr = [];
      if (value == "1") {
        arr = [
          {
            class: "c4",
            label: "配偶姓名",
            key: "childName",
            type: "input",
            rule: { required: true },
            show: true,
          },
          {
            class: "c4",
            label: "与本人关系",
            key: "childRelation",
            type: "select",
            rule: { required: true },
            show: true,
            options: [
              { name: "妻子", value: "2" },
              { name: "丈夫", value: "3" },
            ],
          },
          {
            class: "c4",
            label: "手机号码",
            key: "childTel",
            type: "input",
            rule: { required: true, type: "phone" },
            show: true,
          },
          {
            class: "c8",
            label: "其他手机号码",
            key: "childOtherTel",
            placeholder: "如无填写无即可，多个号码用英文逗号分隔",
            type: "input",
            rule: { required: true },
            show: true,
          },
          {
            class: "c4",
            label: "身份证号",
            key: "childIdCard",
            type: "input",
            rule: { required: true, type: "cardNo" },
            show: true,
          },
        ];
        this.peiouNoteShow = false;
        this.$set(this.peiouFormValue, "isSpouseNote", "");
      } else {
        this.peiouNoteShow = true;

        // arr=[
        //   {class: "c4", label: "备注", key: "isSpouseNote", type: "select", rule: {required: true},show:true},

        // ]
      }
      this.peiouForm.formItemList = this.peiouForm.formItemList
        .slice(0, 1)
        .concat(arr);
    },
    // 子女选择
    changeChild(value) {
      var arr = [];
      if (value == 1) {
        this.childNoteShow = false;
        this.$set(this.childFormValue, "isChildNote", "");

        // this.childForm.formItemList[0].class = 'c12'
      } else {
        this.childNoteShow = true;

        // arr=[
        //   {class: "c4", label: "原因", key: "isChildNote", type: "select", rule: {required: true},show:true},

        // ]
        // this.childForm.formItemList[0].class = 'c8'
      }
      this.childForm.formItemList = this.childForm.formItemList
        .slice(0, 1)
        .concat(arr);
      if (value == "1") {
        this.childShow = true;
      } else {
        this.childShow = false;
      }
    },
    childDel(index) {
      this.childInfo.list.splice(index, 1);
    },
    async getTip() {
      let res = await operatetip();
      this.util.fileOpen(res.data[0].id);
    },
    closeHandle() {
      if (this.gps.operationStatus == 0) {
        this.handleOptClose();
      }
    },
    uploadFileList(obj) {
      uploadProcessFiles(obj.formData)
        .then(async (res) => {
          obj.content.onSuccess(res, obj.content.file, []);
        })
        .catch((error) => {
          obj.content.onError();
        });
    },
    stepAChange(params) {
      // atype 只是简单的步骤递增，不需要特殊处理
      this.step++;
    },
    step3Change(params) {
      // 如果是非关键岗位且需要设置其他类型为'否'
      if (params && params.setOtherTypesToNo) {
        // 将atype和ctype设置为'否'
        if (this.$refs.refA) {
          this.$refs.refA.isSheji = '0';
        }
        if (this.$refs.refC) {
          this.$refs.refC.isSheji = '0';
        }
        // 非关键岗位在btype确认后直接跳到第4步（手写承诺书）
        if (this.appFormValue?.sysFileList?.length) {
          this.handleNextBtn();
        } else {
          this.step = 4;
        }
        return;
      }else  if (params && params.step) {
            this.step++;
      }else{
         // 关键岗位的正常流程
          if (this.appFormValue?.sysFileList?.length) {
            this.handleNextBtn();
          } else {
            this.step++;
          }

      }

     
    },
    backUpdate() {
      if (this.gps.type == "join") {
        if(this.step > 0){
          if(this.appFormValue.isKeyPost == '是'){
            this.step = 1;
          }else{
            this.step = 2;
          }
        }
        this.viewDialog = false;
      }else{
        this.step = 1;
        this.viewDialog = false;
      }
      
    },
    uploadEmit(obj) {
      Object.assign(this.appFormValue, obj);
      this.handleNextBtn();
    },
    backHandle() {
      this.step--;
      if (!this.step) {
        // this.infoD = true
        this.stepNew = 1;
      }
    },
    async confirmInfo() {
      Promise.all([this.submitForm("infoForm")])
        .then(async () => {
          let formData = JSON.parse(JSON.stringify(this.infoFormValue));
          let res = await updateuserinfo(formData);
          Object.assign(this.appFormValue, {
            belongCompanyName: formData.belongCompanyName,
            applyTrueName: formData.userRealName,
            userCode: formData.userCode,
            belongDepartmentName: formData.belongDepartmentName,
            applyPhone: formData.applyPhone,
            companyAndPosition: formData.companyAndPosition,
          });
          this.infoD = false;
          if (this.step == 0) {
            this.tipD = true;
          }
        })
        .catch(() => {
          this.$message.warning("表单数据校验不通过");
        });
    },
    async startWrite() {
      if (!this.isKnow) {
        this.$message.warning("请确认是否已知晓以上填报要求");
        return;
      }
      // this.step++
      this.tipD = false;
    },
    async handleConfirm() {
      await processNext(
        {
          location:
            this.gps.type == "task" ? this.gps.location : "zlwgjs.update",
          taskId: this.gps.taskId,
          pmInsId: this.gps.pmInsId,
        },
        this.formData
      );
      this.viewDialog = false;
      this.afterClick();
    },
    // 初始化
    async initFun() {
      this.loadForm();
    },
    // 获取工单详情
    loadForm() {
      var data = {
        pmInsId: this.gps.pmInsId,
        location: this.gps.location,
      };
      getFeedFormDetail(data).then((res) => {
        Object.assign(this.appFormValue, res.data);
        Object.assign(this.infoFormValue, res.data);
        if (this.gps.type == "join") {
          this.appForm.formDisabled = true;
          this.step = 0;
          // if (this.gps.operationStatus == 0) {
            this.viewList = res.data.myJObRegulationList;
            this.viewDialog = true;
          // }
          Object.assign(this.peiouFormValue, res.data.spouse);
          this.peiouFormValue.isSpouse = parseInt(res.data.isSpouse);
          this.changePeiou(this.peiouFormValue.isSpouse);
          this.peiouFormValue.isSpouseNote = res.data.isSpouseNote;
          this.$set(this.childFormValue, "isChild", parseInt(res.data.isChild));
          // this.childFormValue.isChild = parseInt(res.data.isChild);
          this.changeChild(res.data.isChild);
          this.childFormValue.isChildNote = res.data.isChildNote;
          const childListLength = Math.min(
            res.data.usChildList?.length || 0,
            4
          );
          for (let i = 0; i < childListLength; i++) {
            var obj = {
              childName: res.data.usChildList[i].childName,
              childRelation: res.data.usChildList[i].childRelation,
              childTel: res.data.usChildList[i].childTel,
              childOtherTel: res.data.usChildList[i].childOtherTel,
              childIdCard: res.data.usChildList[i].childIdCard,
            };
            this.$set(this.childInfo.list, i, obj);
          }
        }
      });
    },
    //封装验证函数
    submitForm(formUser) {
      return new Promise((resolve, reject) => {
        if (this.$refs[formUser]) {
          this.$refs[formUser].$children[0].validate((valid) => {
            if (valid) {
              resolve();
            } else {
              reject(new Error("错误"));
            }
          });
        } else {
          resolve();
        }
      });
    },
    // 流转下一步
    handleNextBtn() {
      Promise.all([this.submitForm("appForm")])
        .then(async () => {
          let flag = true,
            options = [
              { refkey: "refA", type: "atype" },
              { refkey: "refB", type: "btype" },
              { refkey: "refC", type: "ctype" },
            ],
            fontArr = [
              "RelationName",
              "CompanyAndPosition",
              "RelationCompany",
              "BusinessDetail",
              "BusinessTotal",
              "ReformDetail",
              "NoReformDetail",
              // 'Remark'
            ];
          options.forEach((item) => {
            let isSheji = this.$refs[item.refkey].isSheji;
            if (!isSheji) {
              flag = false;
            } else if (isSheji == 1) {
              let dataList = this.$refs[item.refkey].dataList;
              if (dataList.length > 0) {
                let fontAndType = fontArr.map((a) => item.type + a);
                fontAndType.unshift("relationType");
                dataList.forEach((a) => {
                  fontAndType.forEach((b) => {
                    if (
                      (b.indexOf("RelationName") > -1 &&
                        a["relationType"] == 1) ||
                      (b.indexOf("ReformDetail") > -1 &&
                        (a[item.type + "ReformDetail"] ||
                          a[item.type + "NoReformDetail"]))
                    ) {
                    } else if (!a[b]) {
                      flag = false;
                    }
                  });
                });
              } else {
                flag = false;
              }
            }
          });

          if (!flag) {
            this.$alert(
              "请完成所有信息上报并上传手写签字承诺书后提交",
              "温馨提示",
              {
                confirmButtonText: "确认",
              }
            );
            return;
          }

          this.formData = JSON.parse(JSON.stringify(this.appFormValue));
          Object.assign(this.formData, {
            supplierRegulationList:
              this.$refs.refA.isSheji == 1 ? this.$refs.refA.dataList : [],
            businessRelationList:
              this.$refs.refB.isSheji == 1 ? this.$refs.refB.dataList : [],
            liabilityRegulationList:
              this.$refs.refC.isSheji == 1 ? this.$refs.refC.dataList : [],
          });
          console.log(this.formData, "======formData=====");

          let res = await preview(
            {
              location: this.gps.location,
              taskId: this.gps.taskId,
              pmInsId: this.gps.pmInsId,
            },
            this.formData
          );

          this.viewList = res.data.myJObRegulationList;
          this.viewDialog = true;
        })
        .catch(() => {
          this.$alert(
            "请完成所有信息上报并上传手写签字承诺书后提交",
            "温馨提示",
            {
              confirmButtonText: "确认",
            }
          );
        });
    },
    afterClick() {
      if (this.gps.myFrom && this.$router.currentRoute.path == "/workOrder") {
        //单点
        if (this.gps.flushPortalUrl) {
          // 集团单点流转
          var flushPortalUrl = decodeURIComponent(this.gps.flushPortalUrl);
          var params = {
            appcode: this.gps.appcode,
            uniqueId: this.gps.uniqueId,
            itemId: this.gps.itemId,
          };
          var pageUrlNew = this.util.toUrl(flushPortalUrl, params);
          window.location.replace(pageUrlNew);
        } else {
          window.opener = null;
          window.open("", "_self");
          window.close();
        }
      } else {
        if (!this.gps.location) {
          this.$router.push({
            name: "processTask",
          });
        } else {
          this.dialogClose();
        }
      }
    },
    // 关闭
    handleOptClose() {
      if (this.dialogClose) {
        //待办打开
        this.dialogClose();
      } else if (
        this.gps.myFrom &&
        this.$router.currentRoute.path == "/workOrder"
      ) {
        //单点
        //单点
        window.opener = null;
        window.open("", "_self");
        window.close();
      } else {
        // let item = this.tabnav.find(item => item.path === this.$route.path);
        // this.$store.dispatch("CloseTabnav", item).then(res => {
        //   if (item.path === this.$route.path) {
        //     const lastTag = res.slice(-1)[0];
        //     // 前一个 tab-view 页面存在，就跳；不存在就到首页
        //     if (lastTag) {
        //       this.$router.push({ path: lastTag.path });
        //     } else {
        //       this.$router.push({ path: "/mywork/processTask" });
        //     }
        //   }
        // });
      }
    },
    // 流程跟踪
    handleFlowTrack() {
      this.doFlowTrack();
    },
    // 查看意见
    handleViewComments() {
      this.doViewComments();
    },
    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n = this[obj[fun]].call(this, obj, data);
      return n;
    },
  },
};
</script>

<style scoped>
.operate-tip {
  color: rgba(192, 0, 0, 1);
  text-decoration: underline;
}

::v-deep .el-dialog__body .el-table th {
  background: none !important;
  color: #000;
}

::v-deep .el-dialog__body .el-table__cell {
  border-right-color: #000 !important;
  border-bottom-color: #000 !important;
}

::v-deep .el-dialog__body .el-table--border,
::v-deep .el-dialog__body .el-table--group {
  border-right: 1px solid #000;
  border-bottom: 1px solid #000;
  border-color: #000;
}

::v-deep .el-checkbox {
  display: flex;
  align-items: center;
}

::v-deep .el-checkbox .el-checkbox__label {
  font-size: 16px;
  line-height: 20px;
  color: rgba(192, 0, 0, 1);
  font-weight: bold;
}

::v-deep .el-checkbox .el-checkbox__inner {
  border-color: rgba(192, 0, 0, 1);
  width: 20px;
  height: 20px;
}

::v-deep .el-checkbox .el-checkbox__inner:after {
  top: 0;
  left: 5px;
  height: 12px;
  width: 6px;
}

.tip {
  font-size: 14px;
  font-weight: bold;
  line-height: 24px;
  padding: 10px;
  border-bottom: 1px solid #ebebeb;
  border-right: 1px solid #ebebeb;
}

.f-1 {
  font-size: 14px;
  font-weight: bold;
  padding: 10px;
  border: 1px solid #ebebeb;
  border-bottom: none;
  color: #000;
}

.mr50 {
  margin-right: 50px;
}

::v-deep .el-input__count {
  background: rgba(247, 234, 233, 0);
}

.red {
  color: rgba(192, 0, 0, 1);
  cursor: pointer;
}

::v-deep .tableCustom .cell {
  white-space: normal;
  overflow: visible;
  text-overflow: inherit;
}

::v-deep .el-table .cell > .inlineC > .el-button {
  background: transparent;
  border: none;
  margin: 0;
  font-size: 14px;
  color: rgba(192, 0, 0, 1);
  padding: 7px 0px;
  white-space: normal;
}

::v-deep .el-input-group__append {
  background-color: #fff3f3;
  border-color: #fff3f3;
}

::v-deep .upload_D {
  min-width: 100%;
}

::v-deep .upload_Btn .uploadB {
  right: 0;
  left: auto;
}

.m-title {
  width: 100%;
  padding-left: 10px;
  border-left: 4px solid rgba(192, 0, 0, 1);
  padding: 10px;
  font-size: 16px;
  color: #333;
  font-weight: 700;
  line-height: 30px;
  border-bottom: 1px solid #ebebeb;
  border-right: 1px solid #ebebeb;
}

.m-title1 {
  border-left: none;
  justify-content: flex-end;
}
::v-deep .tableForm .addMoney .el-form-item .el-form-item__content {
  margin-left: 0 !important;
  height: 32px !important;
}
::v-deep .tableForm .addMoney .el-form-item .el-form-item__content .el-input {
  height: 100% !important;
}
::v-deep
  .tableForm
  .addMoney
  .el-form-item
  .el-form-item__content
  .el-input
  input {
  height: 100% !important;
  font-size: 12px;
}
::v-deep .tableForm .addMoney .inlineB {
  vertical-align: top;
  border-right: 1px solid #ebebeb;
  border-bottom: 1px solid #ebebeb;
}

.all {
  position: relative;
}
::v-deep .tableForm .addMoney .all:nth-child(1) .el-form-item__label {
  background: #fef6e2;
  border-right: 1px solid #ebebeb;
}
::v-deep .tableForm .addMoney .all:nth-child(1) .el-input__inner {
  background: #fef6e2;
}
::v-deep .tableForm .addMoney .all:nth-child(1) .del-line {
  background: #fef6e2;
}
::v-deep .tableForm .addMoney .all:nth-child(2) .el-form-item__label {
  background: #d9eef4;
  border-right: 1px solid #ebebeb;
}
::v-deep .tableForm .addMoney .all:nth-child(2) .el-input__inner {
  background: #d9eef4;
}
::v-deep .tableForm .addMoney .all:nth-child(2) .del-line {
  background: #d9eef4;
}
::v-deep .tableForm .addMoney .all:nth-child(3) .el-form-item__label {
  background: #edefea;
  border-right: 1px solid #ebebeb;
}
::v-deep .tableForm .addMoney .all:nth-child(3) .el-input__inner {
  background: #edefea;
}
::v-deep .tableForm .addMoney .all:nth-child(3) .del-line {
  background: #edefea;
}
::v-deep .tableForm .addMoney .all:nth-child(4) .el-form-item__label {
  background: #eaecf8;
  border-right: 1px solid #ebebeb;
}
::v-deep .tableForm .addMoney .all:nth-child(4) .el-input__inner {
  background: #eaecf8;
}
::v-deep .tableForm .addMoney .all:nth-child(4) .del-line {
  background: #eaecf8;
}

::v-deep .tableForm .addMoney .del-line {
  position: absolute;
  width: 50px !important;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  background: #fff;
  height: 64px;
  color: #d9001b;
  border-right: 1px solid #ebebeb;
  cursor: pointer;
}
::v-deep .tableForm .addMoney .inputdel .el-form-item__label {
  justify-content: center;
}
</style>