import request from "@/assets/js/request";
import store from "@/store";
import util from "@/assets/js/public";

// 中心地图接口
export function branchMapCount(param) {
    return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/screen/branchMapCount`,
      contentType: "application/json;charset=UTF-8",
      data:param
    });
  }


// 大屏相关接口
// 申请情况概览
export function sbqkOverview(param) {
  return request({
    url: `/${process.env.VUE_APP_APPCODE}/action/dashboard/sbqkOverview`,
    contentType: "application/json;charset=UTF-8",
  });
}
// 全省申报情况概览
export function qssbqkOverview(param) {
  return request({
    url: `/${process.env.VUE_APP_APPCODE}/action/dashboard/qssbqkOverview`,
    contentType: "application/json;charset=UTF-8",
  });
}
// 整改情况概览
export function zgqkOverview(param) {
  return request({
    url: `/${process.env.VUE_APP_APPCODE}/action/dashboard/zgqkOverview`,
    contentType: "application/json;charset=UTF-8",
  });
}
// 涉及情况概览
export function sjqkOverview(param) {
  return request({
    url: `/${process.env.VUE_APP_APPCODE}/action/dashboard/sjqkOverview`,
    contentType: "application/json;charset=UTF-8",
  });
}
// 未完成整改情况概览
export function unzgqkOverview(param) {
  return request({
    url: `/${process.env.VUE_APP_APPCODE}/action/dashboard/unzgqkOverview`,
    contentType: "application/json;charset=UTF-8",
  });
}

// 申报情况-更多查询
export function sbqkDetail(params) {
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/dashboard/sbqkDetail?source=PC&page=${params.page}&size=${params.size}`,
      contentType: "application/json; charset=utf-8",
      data: params
  });
}

// 整改情况-更多查询
export function zgqkDetail(params) {
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/dashboard/zgqkDetail?source=PC&page=${params.page}&size=${params.size}`,
      contentType: "application/json; charset=utf-8",
      data: params
  });
}


// 涉及情况-更多查询
export function sjqkDetail(params) {
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/dashboard/sjqkDetail?source=PC&page=${params.page}&size=${params.size}`,
      contentType: "application/json; charset=utf-8",
      data: params
  });
}


// 未完成整改情况-更多查询
export function unzgqkDetail(params) {
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/dashboard/unzgqkDetail?source=PC&page=${params.page}&size=${params.size}`,
      contentType: "application/json; charset=utf-8",
      data: params
  });
}






