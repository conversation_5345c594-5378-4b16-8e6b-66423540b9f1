<template>
  <div :class="'p10'">
    <div class="flex" style="margin-top: 8px;align-items: flex-start">
      <sb-el-form
          style="width: calc(100% - 150px);margin-right: 10px"
          :form="queryForm"
          v-model="listQuery"
          :from="true"
      ></sb-el-form>
      <el-button type="primary" size="small" @click="getList()">查询</el-button>
      <el-button type="primary" size="small" @click="handleDaochu()">导出</el-button>
    </div>
    <el-table
        v-loading.fullscreen.lock="tableLoading"
        element-loading-text="请稍后，正在查询..."
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(255, 255, 255, 0.5)" class="tableCustom" :data="dataList" style="width: 100%;"
        border :cell-style="{background: '#ffffff'}">
      <el-table-column type="index" label="序号" width="80" align="center">
      </el-table-column>
      <el-table-column prop="trueName" label="员工姓名" align="center">
      </el-table-column>
      <el-table-column prop="employeeId" label="员工编号" align="center">
      </el-table-column>
      <el-table-column prop="childName" label="亲属姓名" align="center">
      </el-table-column>
       <el-table-column prop="childRelation" label="与本人关系" align="center">
      </el-table-column>
       <el-table-column prop="childTel" label="手机号码" align="center">
      </el-table-column>
      <el-table-column prop="childIdCard" label="身份证号" align="center">
      </el-table-column>
      <!-- <el-table-column prop="STATUS" label="填报完成情况" align="center">
        <template v-slot:default="scope">
          {{scope.row.STATUS == 12 ? '已提交' : '未提交'}}
        </template>
      </el-table-column> -->
    </el-table>
    <div class="pagination-container">
      <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          layout="total,sizes,prev,pager,next,jumper"
          :page-size="listQuery.size"
          :page-sizes="[10, 20, 50]"
          :hide-on-single-page="total === 0"
          :current-page="listQuery.page"
          :total="total"
          :pager-count="5"
      ></el-pagination>
    </div>
  </div>
</template>
<script>
import {
  cuiban,
  cuibanquery, baseExportQuery, baseGetQueryPage,getYearDict
} from "@/api/process";

export default {
  name: "application",
  data() {
    return {
      nowTime: this.util.getNow("yyyy-MM-dd hh:mm:ss"),

      dataList: [],

      queryForm: {
        inline: true,
        labelPosition: 'right',
        labelWidth: "90px",
        formItemList: [
          // {label: "条线名称", key: "lineName", type: "input", class: 'c3'},
          {label: "单位名称", key: "belongCompanyName", type: "input", class: 'c3'},
          {label: "部门名称", key: "belongDepartmentName", type: "input", class: 'c3'},
          {label: "员工姓名", key: "trueName", type: "input", class: 'c3'},
            {label: "申报年度", key: "year", type: "select", class: 'c3',options:[],},

        ],
      },
      listQuery: {size: 10, page: 1, trueName: '', belongCompanyName: '', belongDepartmentName: ''},
      total: 0,
      exportId: '',
      tableLoading: false,
      tableKey: 0
    }
  },
  async created() {
    this.getYear();
    await this.getList()
  },
  methods: {
     getYear(){
      getYearDict().then(res=>{
          this.queryForm.formItemList[3].options = res.data
          if(res.data.length>0){
            // this.queryForm.formItemList[2].options.unshift({name:'请选择',value:''})
 
          }
 

      })

    },
    async handleCuiban() {
      if (!this.dataList.length) {
        this.$alert('当前无需催办', '提示', {
          confirmButtonText: '我知道了'
        })
        return
      }

      let res = await cuibanquery(this.dataList[0].applyNumber)
      if (res.data > 0) {
        this.$confirm(
            `目前还有${res.data}人暂未提交，点击确认按钮进行催办。`,
            "温馨提示",
            {
              confirmButtonText: "确认"
            }
        ).then(async () => {
          await cuiban(this.dataList[0].applyNumber)
        })
      } else {
        this.$alert('当前无需催办', '提示', {
          confirmButtonText: '我知道了'
        })
      }
    },
    handleSizeChange(val) {
      this.listQuery.size = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.listQuery.page = val;
      this.getList();
    },
    async handleDaochu() {
      let res = await baseExportQuery(this.listQuery)
      if (res.data) {
        this.util.blobDownload(res.data, res.filename);
      } else {
        this.$message({
          message: '导出失败',
          type: 'warning',
          duration: 1500
        });
      }
    },
    async getList() {
      this.tableLoading = true
      let list = await baseGetQueryPage(this.listQuery)
      this.dataList = list.data.content;
      let selfRelation = [
        {name: '本人', value: '1'},
        {name: '妻子', value: '2'},
        {name: '丈夫', value: '3'},
        {name: '长子', value: '4'},
        {name: '长女', value: '5'},
        {name: '儿媳', value: '6'},
        {name: '女婿', value: '7'},
        {name: '次子', value: '8'},
        {name: '次女', value: '9'},

      ]
      this.dataList?.forEach((item) => {
        item.childRelation = selfRelation.find((i) => i.value === item.childRelation)?.name || ''
      })
      this.total = list.data.totalElements
      this.tableKey++
      this.tableLoading = false
    },
    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n = this[obj[fun]].call(this, obj, data);
      return n;
    }
  }

};
</script>
<style scoped>

::v-deep .el-form-item__content {
  flex: 1;
}

::v-deep .tableCustom .f-4 span {
  white-space: normal;
  overflow: visible;
  text-overflow: inherit;
  width: 100%;
}

::v-deep .tableCustom .cell {
  white-space: normal;
  overflow: visible;
  text-overflow: inherit;
}

::v-deep .el-table .cell > .inlineC > .el-button {
  background: transparent;
  border: none;
  margin: 0;
  font-size: 14px;
  color: rgba(192, 0, 0, 1);
  padding: 7px 0px;
  white-space: normal;
}

.m-title {
  width: 100%;
  padding-left: 10px;
  border-left: 4px solid rgba(192, 0, 0, 1);
  padding: 10px;
  font-size: 16px;
  color: #333;
  font-weight: 700;
  line-height: 30px;
  border-bottom: 1px solid #ebebeb;
  border-right: 1px solid #ebebeb;
}
</style>