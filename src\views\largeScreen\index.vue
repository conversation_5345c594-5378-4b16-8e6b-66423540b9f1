<template>
  <div class="screen-wrapper">
    <div class="screen" id="screen" ref="container">
      <div class="close"  @click="closeFun()">关闭</div>

      <div class="top_b">
        <img src="./top2.png" alt="" />
      </div>
      <div class="content flexb">
        <div class="left_b">
          <div class="up">
            <div class="s_tit"><span>申报情况概览</span><span class="more" @click="moreSB()">查看更多>></span></div>
            <div class="line" style="width:100%;border: 1px solid #f8f9fb;height: 1px;margin-top:10px"></div>
            <div class="charta chart1" ref="chart1"></div>
            <div class="pie1" ref="pie1"></div>
          </div>
          <div class="down">
            <!-- <div class="s_tit">涉及情况概览<span class="more" @click="moreSJ()">查看更多>></span></div>
            <div class="line" style="width:100%;border: 1px solid #f8f9fb;height: 1px;margin-top:10px"></div>
            <div class="charta chart2"  ref="chart2"></div> -->
            <div class="s_tit">整改情况概览<span class="more" @click="moreZG()">查看更多>></span></div>
            <div class="line" style="width:100%;border: 1px solid #f8f9fb;height: 1px;margin-top:10px"></div>
            <div class="charta chart3" ref="chart3"></div>
          </div>
        </div>
        <div class="mid_b">
          <div class="up">
            <div class="top">
              <div class="s_tit">全省申报情况概览</div>
              <div class="num">
                    <div class="item flexBetween">
                        <span>总人数</span>
                        <span>{{mapObj.TOTAL}}人</span>
                    </div>
                    <div class="item flexBetween">
                        <span>已申报人数</span>
                        <span>{{mapObj.SBTOTAL}}人</span>
                    </div>
                    <div class="item flexBetween">
                        <span>不涉及人数</span>
                        <span>{{mapObj.UNINVOLVE}}人</span>
                    </div>
                </div>

            </div>
            <div ref="map" id="map"></div>          
          </div>
        </div>
        <!-- <div class="right_b">
          <div class="up">
            <div class="s_tit">整改情况概览<span class="more" @click="moreZG()">查看更多>></span></div>
            <div class="line" style="width:100%;border: 1px solid #f8f9fb;height: 1px;margin-top:10px"></div>
            <div class="charta chart3" ref="chart3"></div>
          </div>
          <div class="down">
            <div class="s_tit">未完成整改情况概览<span class="more" @click="moreUNZG()">查看更多>></span></div>
            <div class="line" style="width:100%;border: 1px solid #f8f9fb;height: 1px;margin-top:10px"></div>
            <div class="charta chart4"  ref="chart4"></div>
          </div>
        </div> -->
      </div>
    </div>


    <!-- 查看更多的弹窗 -->
   <el-dialog title="申报情况概览" :visible.sync="showSB" v-dialogDrag :close-on-click-modal="false" append-to-body :fullscreen="true">
      <SbDialog :key="akey1"  @closeshowDialog="closeshowDialog"></SbDialog>
    </el-dialog>

    <el-dialog title="涉及情况概览" :visible.sync="showSJ" v-dialogDrag :close-on-click-modal="false" append-to-body :fullscreen="true">
      <SjDialog :key="akey2"  @closeshowDialog="closeshowDialog"></SjDialog>
    </el-dialog>

    <el-dialog title="整改情况概览" :visible.sync="showZG" v-dialogDrag :close-on-click-modal="false" append-to-body :fullscreen="true">
      <ZgDialog :key="akey3"  @closeshowDialog="closeshowDialog"></ZgDialog>
    </el-dialog>

    <el-dialog title="未完成整改情况概览" :visible.sync="showUNZG" v-dialogDrag :close-on-click-modal="false" append-to-body :fullscreen="true">
      <UnzgDialog :key="akey4"  @closeshowDialog="closeshowDialog"></UnzgDialog>
    </el-dialog>

  </div>
</template>

<script>
import { mapGetters } from "vuex";
import henanData from "./henan.js";
import {sbqkOverview,qssbqkOverview,zgqkOverview,sjqkOverview,unzgqkOverview} from "@/api/home"
import SbDialog from "@/components/myCom/sbDialog";
import SjDialog from "@/components/myCom/sjDialog";
import ZgDialog from "@/components/myCom/zgDialog";
import UnzgDialog from "@/components/myCom/unzgDialog";

export default {
  name: "largeScreen",
  computed: {
    ...mapGetters(["user"]),
  },
  components:{SbDialog,SjDialog,ZgDialog,UnzgDialog},
  created() {
    
  },
  mounted() {
    // 初始化自适应  ----在刚显示的时候就开始适配一次
    this.handleScreenAuto();
    // 绑定自适应函数   ---防止浏览器栏变化后不再适配
    // window.onresize = () => this.handleScreenAuto();
    window.addEventListener('resize', this.handleScreenAuto);
    this.markChart1();
    // this.markChart2();
    this.markChart3();
    // this.markChart4();

    this.mapEchartsFun()
  },

  destroyed() {
    window.removeEventListener('resize', this.scaleScreen);
  },
  data() {
    return {
      data1: {},
      data2: {},
      data3: {},
      data4: {},
      data5: {},

      option:{},
      item:{},
      akey1:0,
      akey2:0,
      akey3:0,
      akey4:0,
      mapObj:{},

      showZG:false,
      showUNZG:false,
      showSB:false,
      showSJ:false,
    };
  },
  methods: {
    moreSB(){
      this.akey1++
      this.showSB = true
    },

    moreSJ(){
      this.akey2++
      this.showSJ = true
    },

    moreZG(){
      this.akey3++
      this.showZG = true
    },

    moreUNZG(){
      this.akey4++
      this.showUNZG = true
    },

    closeshowDialog(){
      this.showZG=false
      this.showUNZG=false
      this.showSB=false
      this.showSJ=false
    },

    myPie1(arr2,arr3){
      let num2 = arr2.reduce((accumulator, currentValue) => accumulator + currentValue, 0); //涉及
      let num3 = arr3.reduce((accumulator, currentValue) => accumulator + currentValue, 0); //不涉及
      const pie1 = this.$refs.pie1;
      if (pie1) {
        const myChart = this.$echarts.init(pie1);
        var option = {
            tooltip: {
              trigger: 'item'
            },
            legend: {
              show: false,
              top: '5%',
              left: 'center'
            },
            series: [
              {
                name: '',
                type: 'pie',
                radius: ['50%', '70%'],
                avoidLabelOverlap: false,
                itemStyle: {
                  borderRadius: 10,
                  borderColor: '#fff',
                  borderWidth: 2,
                  normal: {
                    color: function(params) {
                      // 预定义一个颜色数组
                      var colorList = [
                        '#f1bfbb',
                        '#ec5547',
                      ];
                      // 返回每个饼图扇区的颜色
                      return colorList[params.dataIndex];
                    }
                  }
                },
                label: {
                    normal: {
                        position: 'outside', // 标签在扇形外侧
                        formatter: function (params) {
                            return params.name.substring(0,2)+'\n'+params.name.substring(2,params.name.length)+',\n'+params.value;
                        }
                        // 其他标签样式设置
                    }
                },
                labelLine: {
                    normal: {
                        show: true, // 不显示标签指引线
                        length: 2, // 引导线的长度
                        length2: 5 // 第二段引导线的长度
                    }
                },
                data: [
                  { value: num2, name: '涉及人数' },
                  { value: num3, name: '不涉及人数' },
                ]
              }
            ]
        };
        option && myChart.setOption(option);
      }
    },

    //#region
    markChart1() {
      const chart1 = this.$refs.chart1;
      if (chart1) {
        const myChart = this.$echarts.init(chart1);
        var option = {
             tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'none'
                    }
                },
                legend: {
                    bottom: 10,
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    top: '8%',
                    bottom: '10%',
                    containLabel: true
                },
                xAxis: [
                    {
                        type: 'category',
                        data: [],
                        splitLine: {
                            show: false // 不显示x轴的网格线  
                        },
                        axisLabel: {
                            rotate: 45, // 旋转角度为 20 度
                            margin: 12
                        },
                        axisTick: { show: false },
                    }
                ],
                yAxis: [
                    {
                        type: 'value',
                        splitLine: {
                            show: false // 不显示Y轴的网格线  
                        },
                        axisLine: { show: false },
                        axisTick: { show: false },
                    }
                ],
                series: [
                    {
                        name: '涉及人数',
                        type: 'bar',
                        stack: 'Ad',
                        emphasis: {
                            focus: 'series'
                        },
                        itemStyle: {
                            color: '#F1BFBB',
                        },
                        barWidth:'',
                        data: []
                    },
                    {
                        name: '不涉及人数',
                        type: 'bar',
                        stack: 'Ad',
                        emphasis: {
                            focus: 'series'
                        },
                        itemStyle: {
                            color: '#EC5547',
                        },
                        data: []
                    },
                ]
        };
        sbqkOverview().then((res) => {
          let arr1 = [];
          let arr2 = [];
          let arr3 = [];
          res.data.forEach((item,i)=>{
            arr1.push(item.COMPANY)
            arr2.push(item.INVOLVE)
            arr3.push(item.UNINVOLVE)
          })
          option.xAxis[0].data = arr1
          option.series[0].data = arr2
          option.series[1].data = arr3
          option && myChart.setOption(option);

          this.myPie1(arr2,arr3)
        });
      }
    },
    markChart2() {
      const chart2 = this.$refs.chart2;
      if (chart2) {
        const myChart = this.$echarts.init(chart2);
        var option = {
               tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'none',
                        crossStyle: {
                            color: '#999'
                        }
                    }
                },
                legend: {
                    bottom: 0,
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    top: '8%',
                    containLabel: true
                },
                xAxis: [
                    {
                        type: 'category',
                        data: [],
                        splitLine: {
                            show: false // 不显示x轴的网格线  
                        },
                        axisLabel: {
                            rotate: 45, // 旋转角度为 20 度
                            margin: 16
                        },
                        axisTick: { show: false },
                    }
                ],
                yAxis: [
                    {
                        type: 'value',
                        axisLine: { show: false },
                        axisTick: { show: false },

                    },
                    {
                        type: 'value',
                        axisLine: { show: false },
                        axisTick: { show: false },
                        axisLabel: {
                            formatter: `{value} %`
                        }
                    },
                ],
                series: [
                    {
                        name: '未完成整改数量',
                        type: 'bar',
                        stack: 'A',
                        emphasis: {
                            focus: 'series'
                        },
                        itemStyle: {
                            color: '#F1BFBB',
                        },
                        data: []
                    },
                    {
                        name: '占比',
                        type: 'line',
                        stack: 'Ad',
                        emphasis: {
                            focus: 'series'
                        },
                        itemStyle: {
                            color: '#EC5547',
                        },
                        yAxisIndex: 1,
                        tooltip: {
                            valueFormatter: function (value) {
                                return value + ' %';
                            }
                        },
                        data: []
                    },
                ]
        };
        sjqkOverview().then((res) => {
          let arr1 = [];
          let arr2 = [];
          let arr3 = [];
          res.data.forEach((item,i)=>{
            arr1.push(item.COMPANY)
            arr2.push(item.INCOMPLETE)
            arr3.push(item.PERCENTNUM)
          })
          option.xAxis[0].data = arr1
          option.series[0].data = arr2
          option.series[1].data = arr3
          option && myChart.setOption(option);
        });
      }
    },
    markChart3() {
      const chart3 = this.$refs.chart3;
      if (chart3) {
        const myChart = this.$echarts.init(chart3);
        var option = {
             tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'none'
                    },
                    position: function (point, params, dom, rect, size) {
                        return [point[0]+15, point[1]+5]; 
                    },
                },
                legend: {
                    bottom: 0,
                    left: 0,
                    orient: 'vertical',
                    itemGap: 3
                },
                grid: {
                    left: '1%',
                    right: '4%',
                    bottom: '15%',
                    top:'2%',
                    containLabel: true
                },
                xAxis: [
                    {
                      type: 'value',
                      splitLine: {
                          show: true // 显示Y轴的网格线  
                      },
                      axisLine: { show: false },
                      axisTick: { show: false },
                        
                    }
                ],
                yAxis: [
                    {
                        type: 'category',
                        data: [],
                        splitLine: {
                            show: false // 不显示x轴的网格线  
                        },
                        axisLabel: {
                            rotate: 0, // 旋转角度为 20 度
                            margin: 16
                        },
                        axisTick: { show: false },
                    }
                ],
                series: [
                    {
                        name: '本人及相关亲属（配偶、子女及其配偶）违规持有供应商或渠道商股权情况',
                        type: 'bar',
                        stack: 'A',
                        emphasis: {
                            focus: 'series'
                        },
                        itemStyle: {
                            color: '#F1BFBB',
                        },
                        data: []
                    },
                    {
                        name: '本人及相关亲属（配偶、子女及其配偶）创办、参股或实际控制的企业，与中国移动及关联企业发生业务往来情况',
                        type: 'bar',
                        stack: 'A',
                        emphasis: {
                            focus: 'series'
                        },
                        itemStyle: {
                            color: '#EC5547',
                        },
                        data: []
                    },
                    {
                        name: '本人及相关亲属（配偶、子女及其配偶）在与中国移动及关联企业有业务关系的非国有企业中，负责移动合作业务情况',
                        type: 'bar',
                        stack: 'A',
                        emphasis: {
                            focus: 'series'
                        },
                        itemStyle: {
                            color: '#AC4D59',
                        },
                        data: []
                    },
                ]
        };
        zgqkOverview().then((res) => {
           let arr0 = [];
           let arr1 = [];
          let arr2 = [];
          let arr3 = [];
          res.data.forEach((item,i)=>{
            arr0.push(item.COMPANY)
            arr1.push(item.TYPEA)
            arr2.push(item.TYPEB)
            arr3.push(item.TYPEC)
          })
          option.yAxis[0].data = arr0
          option.series[0].data = arr1
          option.series[1].data = arr2
          option.series[2].data = arr3
          option && myChart.setOption(option);
        });
      }
    },
    markChart4() {
      const chart4 = this.$refs.chart4;
      let data4 = [];
      if (chart4) {
        const myChart = this.$echarts.init(chart4);
        var option = {
          tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'none'
                    }
                },
                legend: {
                    bottom: 0,
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    top: '8%',
                    bottom: '10%',
                    containLabel: true
                },
                xAxis: [
                    {
                        type: 'category',
                        data: [],
                        splitLine: {
                            show: false // 不显示x轴的网格线  
                        },
                        axisLabel: {
                            rotate: 45, // 旋转角度为 20 度
                            margin: 16
                        },
                        axisTick: { show: false },
                    }
                ],
                yAxis: [
                    {
                        type: 'value',
                        splitLine: {
                            show: true // 显示Y轴的网格线  
                        },
                        axisLine: { show: false },
                        axisTick: { show: false },
                    }
                ],
                series: [
                    {
                        name: '已完成整改数量',
                        type: 'bar',
                        stack: 'Ad',
                        emphasis: {
                            focus: 'series'
                        },
                        itemStyle: {
                            color: '#EC5547',
                        },
                        data: []
                    },
                    {
                        name: '未完成整改数量',
                        type: 'bar',
                        stack: 'Ad',
                        emphasis: {
                            focus: 'series'
                        },
                        itemStyle: {
                            color: '#5B8ED9',
                        },
                        data: []
                    },
                ]
        };
        unzgqkOverview().then((res) => {
          let arr1 = [];
          let arr2 = [];
          let arr3 = [];
          res.data.forEach((item,i)=>{
            arr1.push(item.COMPANY)
            arr2.push(item.COMPLETENUM)
            arr3.push(item.INCOMPLETENUM)
          })
          option.xAxis[0].data = arr1
          option.series[0].data = arr2
          option.series[1].data = arr3

          option && myChart.setOption(option);
        });
      }
    },
 
    //#endregion

    //地图渲染
    mapEchartsFun(areaName) {
      let mymapChart = this.$echarts.init(this.$refs.map);
      this.$echarts.registerMap("henan", henanData);

      // 定义标记点的数据
      let optionMap = {
        legend: {},
        tooltip:{
          show:true,
          triger: 'item',
          formatter: function(params, ticket, callback) {
            if(params.componentType!="markPoint") {
              if (params.componentType == "series") {
                  return  params.data.name + "-已申报人数：" + params.data.ALLINVOLVE+'<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-不涉及人数：' + params.data.UNINVOLVE;
              } else if (params.componentType == "markPoint") {
                return params.data.name + "-已申报人数：" + optionMap.series[0].data[0].ALLINVOLVE+'<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-不涉及人数：' + optionMap.series[0].data[0].UNINVOLVE;
              }
            } else {
              return params.data.name + "-涉及人数：" + params.data.value
            }
          }
        },
        visualMap: [
          {
            type: 'continuous',
            min: 0,
            max: 100,
            text: ['高', '低'],
            right: '15%',
            bottom: 30,
            itemWidth: 30, // 视觉映射组件的宽度，对于连续型是颜色条的宽度
            itemHeight: 200, // 视觉映射组件的高度，对于连续型是颜色条的高度
            realtime: false,
            calculable: true,
            realtime: true, // 拖动滚动条时是否实时更新图表
            inRange: {
                color: ['#29cee3', '#9efd1e', '#ff9e1f', '#fc031d']
            },
            textStyle: {       // 文字颜色
                fontSize: 13,         // 文字字号
                fontWeight: 'bold'    // 文字粗细
                // 其他文字样式属性如 fontFamily, fontStyle 等也可以在这里设置
            },
          },
          {
            name: '涉及人员',
            type: 'piecewise', // 分段型 visualMap
            textStyle: {       // 文字颜色
                fontSize: 13,         // 文字字号
                fontWeight: 'bold'    // 文字粗细
                // 其他文字样式属性如 fontFamily, fontStyle 等也可以在这里设置
            },
            pieces: [
              { min: 0, max: 10, label: '0 - 10', color: '#29cee3' ,symbol: 'circle'},
              { min: 10, max: 30, label: '10 - 30', color: '#9efd1e' ,symbol: 'circle'},
              { min: 30, max: 50, label: '30 - 50', color: '#ff9e1f',symbol: 'circle' },
              { min: 50, max: 100, label: '50以上', color: '#fc031d' ,symbol: 'circle'},
              { min: 100, max: 1000, label: '涉及人员', color: '#ffffff' ,textStyle: { fontWeight: 'blod',fontSize: '16'}}
              // 可以根据需要添加更多区间
            ],
            right: '5%',
            bottom: 50,
          },
        ],
        series: [
          {
            type: "map",
            map: "henan",
            aspectScale: 0.85, //用于 scale 地图的长宽比，如果设置了projection则无效
            zoom: 1.21, //当前视角的缩放比例
            roam: false, //是否开启鼠标缩放和平移漫游。默认不开启。如果只想要开启缩放或者平移，可以设置成 'scale' 或者 'move'。设置成 true 为都开启
            dragEnable: false, // 地图是否可通过鼠标拖拽平移，默认为true
            zoomEnable: true, //地图是否可缩放，默认值为true
            hoverable: false,
            left: '10%', // 
            label: {
              normal: {
                show: true,
              },
              emphasis: {
                show: true,
              },
            },
            itemStyle: {
              //地图区域的多边形 图形样式。
              normal: {
                areaColor: "#ffebec",
                borderColor: "#d67a79",
                borderWidth: 1.5,
              },
              emphasis: {
                areaColor: "#d67a79", //鼠标经过区域颜色
                label: {
                  //鼠标经过区域内文字样式
                  show: true,
                  textStyle: { color: "#fff", fontSize: 14, fontWeight: 700 },
                },
              },
            },
            data: [],
            markPoint: {
              // symbol: "triangle", // 标记点的图形
              symbolSize: 16, // 标记点的大小
              label: {
                show: false,
              },
              itemStyle: {
                normal: {
                    borderWidth: 3,            // 标注边线线宽，单位px，默认为1
                    label: {
                        show: false
                    }
                  },
                  emphasis: {
                      borderColor: '#1e90ff',
                      borderWidth: 5,
                      label: {
                          show: false
                      }
                  }
              },
              data: [
                {
                  name: "省公司",
                  branchCode:'4772338661636601428',
                  coord: [113.535912, 34.757975],
                  symbol: "triangle", // 标记点的图形
                  itemStyle: {
                    color: "red",
                  },
                  label: {
                    show: true, // 显示标记点的文本
                    formatter: "{b}", // 标记点文本的格式化字符串，这里使用{name}表示取数据项的name属性
                    position: "right", // 标记点文本的位置，可以是'top'、'bottom'、'left'、'right'等
                  },
                },
                {name: '郑州市', coord: [113.265412, 34.507975], },
                {name: '南阳市', coord: [112.205322, 32.990833], },
                {name: '周口市', coord: [114.649653, 33.700357], },
                {name: '洛阳市', coord: [111.804468, 34.263041], },
                {name: '商丘市', coord: [115.541886, 34.318589], },
                {name: '信阳市', coord: [114.591023, 32.007848], },
                {name: '新乡市', coord: [113.983991, 35.302616], },
                {name: '驻马店市', coord: [113.924736, 32.880169], },
                {name: '安阳市', coord: [114.122392, 36.067577], },
                {name: '开封市', coord: [114.341447, 34.555049], },
                {name: '平顶山市', coord: [112.700661, 33.686169], },
                {name: '许昌市', coord: [113.506063, 34.002956], },
                {name: '濮阳市', coord: [115.041299, 35.868234], },
                {name: '焦作市', coord: [112.908266, 35.12904] , },
                {name: '漯河市', coord: [113.690539, 33.661413], },
                {name: '三门峡市', coord: [110.604099, 34.407338], },
                {name: '鹤壁市', coord: [114.16777, 35.665426], }, 
                {name: '济源市', coord: [112.180047, 35.090378], },
              ],
            },
          },
        ],
      };

      qssbqkOverview().then(res=>{
        this.mapObj = res.data.total
        var json = JSON.parse(JSON.stringify(res.data.map).replace(/COMPANY/g, "name"));
        for(var i in json){
          if(json[i].name !== '省公司'){
            json[i].name =  json[i].name.substr(0, json[i].name.length - 3) + '市'
          }
        }
        optionMap.series[0].data = json
        optionMap.series[0].markPoint.data.forEach(item => {
          json.forEach(ele => {
            if(item.name == ele.name) {
              item.value = ele.INVOLVE || 0;
              if(item.name!="省公司") {
                if (item.value >= 0 && item.value< 10) {
                  item.itemStyle = {
                    color: "#ffebec",
                    borderColor: '#29cee3',
                  }
                } else if (item.value >= 10 && item.value< 30) {
                    item.itemStyle = {
                      color: "#ffebec",
                      borderColor: '#9efd1e',
                    }
                } else if (item.value >= 30 && item.value< 50) {
                    item.itemStyle = {
                      color: "#ffebec",
                      borderColor: '#ff9e1f',
                    }
                } else if (item.value >= 50) {
                    item.itemStyle = {
                      color: "#ffebec",
                      borderColor: '#fc031d',
                    }
                }
              }
            }
          })
        })
        let maxValue = json.reduce((max, current) => {
          return Math.max(max, current.INVOLVE);
        }, json[0].INVOLVE);
        mymapChart.setOption(optionMap);
      })
    },
    // #region

    closeFun() {
        this.$emit('closeDia')
    },


    //数据大屏自适应函数
    handleScreenAuto() {
        const container = this.$refs.container;
        const scaleX = window.innerWidth / container.offsetWidth;
        const scaleY = window.innerHeight / container.offsetHeight;
        const scale = Math.min(scaleX, scaleY);
        container.style.transform = `scale(${scale})`;

        // 居中
        const left = (window.innerWidth - container.offsetWidth * scale) / 2;
        const top = (window.innerHeight - container.offsetHeight * scale) / 2;
        container.style.position = 'absolute';
        container.style.left = `${left}px`;
        container.style.top = `${top}px`;
    },
  },
};
</script>

<style scoped>
div {
  /* border: 1px solid #c52121; */
}
.top_b {
  width: 100%;
  margin-top: 10px;
  background-color: #fff;
  position: relative;
}
.top_b img {
  width: 100%;
}
.screen {
  display: inline-block;
  width: 1980px;
  height: 945px;
  transform-origin: 0 0;
  position: absolute;
  left: 50%;
}
.flexb {
  display: flex;
  justify-content: space-between;
}
.content {
  width: 100%;
  height: calc(100% - 70px);
  padding: 10px;
  background-color: #f8f9fb;
  /* background-color: #1a55c9; */
}
.content .left_b,
.content .right_b {
  width: 35%;
  height: 100%;
  border-radius: 5px;
}

.content .mid_b {
  width: 64.5%;
  height: 100%;
  border-radius: 5px;
}

.mid_b .up {
  width: 100%;
  height: 100%;
  padding: 10px;
  background-color: #fff;
}
.mid_b .up .top {
  width: 100%;
  height: 6%;
  padding: 10px;
  /* background-color: rgb(221, 50, 50); */
}
.left_b .up,
.left_b .down {
  width: 100%;
  height: 43.5%;
  padding: 10px;
  background-color: #fff;
  margin-bottom: 10px;
}
.left_b .down{
  width: 100%;
  height: 55.5%;
  padding: 10px;
  background-color: #fff;
  margin-bottom: 10px;
}


.right_b .up,
.right_b .down{
  width: 100%;
  height: 49.5%;
  padding: 10px;
  background-color: #fff;
  margin-bottom: 10px;
}

.chart1,
.chart2,
.chart3,.chart4{
  height: 90%;
}

.pie1 {
  position: absolute;
  top: 15%;
  left: 22%;
  width: 12%;
  height: 16%;
}


.s_tit {
  font-size: 18px;
  border-left: 3px solid #ba020f;
  padding-left: 5px;
  font-weight: 700;
  color: #ba020f;
}
.s_tit .more{
  font-size: 14px;
  color: #8c8c8c;
  float: right;
  cursor: pointer;
}


#map {
  width: 96%;
  height: 95%;
  /* background: #04be02; */
}

.close{
  position: absolute;
  z-index: 999999;
  right: 50px;
  top: 26px;

  width: 60px;
  height: 30px;
  text-align: center;
  background: #ac4d59;
  color: #fff;
  line-height: 30px;
  font-size: 16px;
  cursor: pointer;
}

.flexBetween {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.num {
  /* display: flex;
  justify-content: space-between;
  align-items: center; */
  width: 20%;
  float: right;
}

.num .item {
    width: calc(100% - 10px);
    padding: 30px 10px;
    border-radius: 10px;
    font-size: 20px;
    margin-top: 20px;
}

.num .item span:first {
    font-weight: 500;
}

.num .item:nth-child(1) {
    border: 1px solid #DAEEE8;
    background-image: linear-gradient(90deg, #D8F8F3, #E9FCF8);
    color: #60b1a5;
}

.num .item:nth-child(2) {
    border: 1px solid #ECD096;
    background-image: linear-gradient(90deg, #FFE2AA, #FEF4DD);
    color: #D19F40;
}

.num .item:nth-child(3) {
    border: 1px solid #F7E7D7;
    background-image: linear-gradient(90deg, #FCE9DF, #FEF4EF);
    color: #B18071;
}

</style>