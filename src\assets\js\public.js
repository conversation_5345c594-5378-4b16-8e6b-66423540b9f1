import {JSEncrypt} from "jsencrypt";
import Layout from "@/views/layout/Layout";
import AppMain from "@/views/layout/components/AppMain";
import WorkOrder from "@/components/WorkOrder";
import router from "@/router";
import store from "@/store";
import {appHtml, appName} from "@/assets/js/process.config.js";

const CryptoJS = require('crypto-js');  //引用AES源码js
var allnewArr = []
const util = {
    Decrypt(word) {
        const key = CryptoJS.enc.Utf8.parse("dW5uR,Yml#y%PeLG");  //十六位十六进制数作为密钥
        const iv = CryptoJS.enc.Utf8.parse('Be*Kn0xJ&XHc(Jl0');   //十六位十六进制数作为密钥偏移量
        let decrypt = CryptoJS.AES.decrypt(word, key, {iv: iv, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7});
        let decryptedStr = decrypt.toString(CryptoJS.enc.Utf8);
        return decryptedStr;
    },
    //把后台传得扁平化JSON格式转化为EasyUI Tree树控件的JSON格式 rows:json数据对象;idFieldName:表id的字段名;pidFieldName:表父级id的字段名;fileds:要显示的字段,多个字段用逗号分隔
    toTreeData: function (rows, id, parentid, fileds) {
        function exists(rows, ParentId) {
            for (let i = 0; i < rows.length; i++) {
                if (rows[i][id] == ParentId) return true;
            }
            return false;
        }

        let nodes = [];

        for (let i in rows) {
            let row = rows[i];
            if (!exists(rows, row[parentid])) {
                let data = {};
                data[id] = row[id];
                let arrFiled = fileds.split(",");
                for (let j = 0; j < arrFiled.length; j++) {
                    let arrF = arrFiled[j].split("|");
                    if (arrF[0] != id) data[arrF[0]] = row[arrF[0]];
                    if (arrF[1] && arrF[1] != id) data[arrF[1]] = row[arrF[0]];
                }
                nodes.push(data);
            }
        }
        let toDo = [];
        for (let i = 0; i < nodes.length; i++) {
            toDo.push(nodes[i]);
        }
        while (toDo.length) {
            let node = toDo.shift(); // the parent node
            // get the children nodes
            for (let i = 0; i < rows.length; i++) {
                let row = rows[i];
                if (row[parentid] == node[id]) {
                    //let child = {
                    //    id: row[id]
                    //};
                    let child = {};
                    child[id] = row[id];
                    let arrFiled = fileds.split(",");
                    for (let j = 0; j < arrFiled.length; j++) {
                        let arrF = arrFiled[j].split("|");
                        if (arrF[0] != id) child[arrF[0]] = row[arrF[0]];
                        if (arrF[1] && arrF[1] != id)
                            child[arrF[1]] = row[arrF[0]];
                    }
                    if (node.children) {
                        node.children.push(child);
                    } else {
                        node.children = [child];
                    }
                    toDo.push(child);
                }
            }
        }
        return nodes;
    },
    toFlatData(treeData, prop) {//treeData（数据）  prop（树结构中子节点字段名，非必传）
        var prop = prop || "children";
        var flatData = [];

        function handlePush(data) {
            if (data && data.length > 0) {
                for (var i in data) {
                    if (data[i][prop] && data[i][prop].length > 0) {
                        handlePush(data[i][prop]);
                    }
                    var item = {};
                    for (var j in data[i]) {
                        if (j != prop) item[j] = data[i][j];
                    }
                    flatData.push(item);
                }
            }
        }

        handlePush(treeData);
        return flatData;
    },
    encrypt: function (val) {
        let encrypt = new JSEncrypt();
        encrypt.setPublicKey(
            "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC+K3y4fL71dFhFYC9c9bea9wPH" +
            "\r" +
            "youU86VI0nI1GtDiMbSd3/mFcf/Z14hixordW8W8Q0BftncjcbIOHOeHDK074hpV" +
            "\r" +
            "bMdJTgadisuksX1fISp5CXa5ETsDcHa6usb1wGd2EFSo8ws5Jfi5oGZVgRzF3YLI" +
            "\r" +
            "KgxYn+NZu7cvHOD0GwIDAQAB" +
            "\r"
        );
        let data = encrypt.encrypt(val);
        return data;
    },
    //替换url.-+
    replaceD: function (str) {
        var len = str.split(".").length + 1;
        for (var i = 0; i < len; i++) {
            str = str.replace(".", "%2E");
        }
        var len = str.split("-").length + 1;
        for (var i = 0; i < len; i++) {
            str = str.replace("-", "%2D");
        }
        var len = str.split("+").length + 1;
        for (var i = 0; i < len; i++) {
            str = str.replace("+", "%2B");
        }
        var len = str.split("/").length + 1;
        for (var i = 0; i < len; i++) {
            str = str.replace("/", "%2F");
        }
        var len = str.split("=").length + 1;
        for (var i = 0; i < len; i++) {
            str = str.replace("=", "%3D");
        }
        return str;
    },
    getApiUrl() {
        let src;
        if (process.env.NODE_ENV == "development") {
            //开发环境
            src = process.env.VUE_APP_DEVBASEURL;
        } else if (process.env.NODE_ENV == "debug") {
            //测试环境
            src = process.env.VUE_APP_DEBBASEURL;
        } else if (process.env.NODE_ENV == "production") {
            //生产环境
            src = process.env.VUE_APP_PROBASEURL;
        }
        return src;
    },
    getFile() {
        let url = window.location.href;
        if (url.indexOf("/" + process.env.VUE_APP_APPCODE) > -1)
            url =
                url.split("/" + process.env.VUE_APP_APPCODE)[0] +
                process.env.VUE_APP_APPCODE;
        else if (url.indexOf("/#/") > -1)
            url = url.split("/#/")[0] + "/" + process.env.VUE_APP_APPCODE;
        return url;
    },
    fileDownload(id) {
        window.open(util.getApiUrl() + "/sys/file/download?id=" + id, "_blank");
    },
    fileOpen(id) {
        window.open(util.getApiUrl() + "/sys/file/open?id=" + id, "_blank");
    },
    windowOpen(url) {
        window.open(util.getFile() + url, "_blank");
    },
    getRoutes(data, li) {
        let rs = [];
        for (let i in data) {
            if (data[i].permissionType !== "BUTTON") {
                let array = data[i].url.split("/");
                let name = array[array.length - 1];
                let route = {
                    path: data[i].url,
                    meta: {
                        title: data[i].description,
                        icon: data[i].icon ? data[i].icon.trim() : "",
                    },
                    component: (resolve) =>
                        require([`@/views${data[i].url}`], resolve),
                };
                if (data[i].query) {
                    route.query = data[i].query;
                }
                if (data[i].children) {
                    if (data[i].menuLevel == 1) {
                        route.component = Layout;
                    } else {
                        route.component = AppMain;
                    }
                    route.name = name;
                    route.redirect = data[i].children[0].url;
                    route.children = util.getRoutes([...data[i].children], li + 1);
                } else {
                    if (data[i].menuLevel == 1) {
                        route.path = '';
                        route.redirect = data[i].url;
                        route.component = Layout;
                        route.children = [];
                        let routei = {};
                        routei.path = data[i].url;
                        routei.name = name;
                        routei.meta = {title: data[i].description, icon: data[i].icon ? data[i].icon.trim() : ""},
                            routei.component = resolve => require([`@/views${data[i].url}`], resolve);
                        route.children.push(routei);
                    } else {
                        // let ar = [];
                        // for (let j = li; j < array.length; j++) {
                        //     ar.push(array[j]);
                        // }
                        // route.path = ar.join("/");
                        route.name = name;
                        if (data[i].menuLevel == 100) route.hidden = true;
                        // 工单重定向到公共组件
                        // if(route.query && route.query.isProcess && route.query.isProcess=="true"){
                        //     route.redirect = "/WorkOrder";
                        // }
                    }
                }
                rs.push(route);
            }
        }
        return rs;
    },
    getQueryString(href) {
        let qs = {};
        let url = href || decodeURIComponent(window.location.href);
        //不管有没有伪静态 都看一下?问号后面的参数
        if (url.indexOf("?") > -1) {
            url = url.substring(url.indexOf("?") + 1);
            let prm = url.split("&");
            for (let p in prm) {
                if (prm[p]) {
                    let sp = prm[p].split("=");
                    if (sp.length > 1) {
                        let spkey = sp[0];
                        let spvalue = sp[1];

                        if (spvalue.indexOf("#") > -1) {
                            spvalue = spvalue.substring(
                                0,
                                spvalue.indexOf("#")
                            );
                        }
                        qs[spkey] = spvalue;
                    }
                }
            }
        }
        return qs;
    },
    toUrl(url, data) {
        let tourl = url;
        for (let i in data) {
            if (tourl.indexOf("?") > -1) tourl += "&" + i + "=" + (data[i] ? data[i] : '');
            else tourl += "?" + i + "=" + (data[i] ? data[i] : '');
        }
        return tourl;
    },
    getDateFormat(value, dateformat, def) {
        if (value && dateformat) {
            let reg = /(\d{4})\S(\d{1,2})\S(\d{1,2})[\S\s](\d{1,2}):(\d{1,2}):(\d{1,2})/;
            let regdate = /(\d{4})\S(\d{1,2})\S(\d{1,2})/;
            if (reg.test(value) && value.toString().length < 20) {
                let result = value.match(reg);
                dateformat = dateformat.replace("yyyy", result[1]); //代表年
                dateformat = dateformat.replace("MM", result[2]); //代表月
                dateformat = dateformat.replace("dd", result[3]); //代表日
                if (dateformat.indexOf("hh") > -1) {
                    dateformat = dateformat.replace("hh", result[4]); //代表时
                } else {
                    dateformat = dateformat.replace("HH", result[4]); //代表时
                }
                dateformat = dateformat.replace("mm", result[5]); //代表分
                dateformat = dateformat.replace("ss", result[6]); //代表秒
                if (dateformat === "diff") {
                    return zjs.getDateDiff(
                        result[1] +
                        "/" +
                        result[2] +
                        "/" +
                        result[3] +
                        " " +
                        result[4] +
                        ":" +
                        result[5] +
                        ":" +
                        result[6]
                    );
                }
                return dateformat;
            } else if (regdate.test(value) && value.toString().length < 20) {
                let result = value.match(regdate);
                dateformat = dateformat.replace("yyyy", result[1]); //代表年
                dateformat = dateformat.replace("MM", result[2]); //代表月
                dateformat = dateformat.replace("dd", result[3]); //代表日
                if (dateformat == "diff") {
                    return getDateDiff(
                        result[1] + "/" + result[2] + "/" + result[3] + ""
                    );
                }
                return dateformat;
            }
        } else {
            if (def) value = def;
        }
        return value;
    },
    //取当前时间返回年月日
    getNow(dateformat, noZero, t) {
        let d = new Date();
        if (t) d = new Date(t);
        if (!dateformat) dateformat = "yyyy-MM-dd";
        dateformat = dateformat.replace("yyyy", d.getFullYear()); //代表年
        dateformat = dateformat.replace(
            "MM",
            noZero
                ? d.getMonth() + 1
                : d.getMonth() > 8
                    ? d.getMonth() + 1
                    : "0" + (d.getMonth() + 1)
        ); //代表月
        dateformat = dateformat.replace(
            "dd",
            noZero
                ? d.getDate()
                : d.getDate() > 9
                    ? d.getDate()
                    : "0" + d.getDate()
        ); //代表日
        if (dateformat.indexOf("hh") > -1) {
            dateformat = dateformat.replace(
                "hh",
                noZero
                    ? d.getHours()
                    : d.getHours() > 9
                        ? d.getHours()
                        : "0" + d.getHours()
            ); //代表时
        } else {
            dateformat = dateformat.replace(
                "HH",
                noZero
                    ? d.getHours()
                    : d.getHours() > 9
                        ? d.getHours()
                        : "0" + d.getHours()
            ); //代表时
        }
        dateformat = dateformat.replace(
            "mm",
            noZero
                ? d.getMinutes()
                : d.getMinutes() > 9
                    ? d.getMinutes()
                    : "0" + d.getMinutes()
        ); //代表分d.getMinutes()
        dateformat = dateformat.replace(
            "ss",
            noZero
                ? d.getSeconds()
                : d.getSeconds() > 9
                    ? d.getSeconds()
                    : "0" + d.getSeconds()
        ); //代表秒d.getSeconds()
        let week = d.getDay();
        let day = "";
        if (week == 0) day = "日";
        else if (week == 1) day = "一";
        else if (week == 2) day = "二";
        else if (week == 3) day = "三";
        else if (week == 4) day = "四";
        else if (week == 5) day = "五";
        else if (week == 6) day = "六";
        dateformat = dateformat.replace("weekday", day);
        return dateformat;
    },
    //时间戳转日期
    getTimeDate(t, dateformat) {
        return util.getNow(dateformat, false, t);
    },
    htmlDecode(value) {
        if (typeof value == "string")
            value = value.replace(new RegExp("&nbsp;", "g"), "&nb-sp;");
        var frameDiv = document.createElement("div");
        frameDiv.innerHTML = value;
        value = frameDiv.innerText;
        value = value.replace(new RegExp("&nb-sp;", "g"), "&nbsp;");
        if (typeof value == "string") {
            var dec = [
                "&ldquo;-“",
                "&rdquo;-”",
                "&lsquo;-‘",
                "&rsquo;-’",
                '&quot;-"',
                "&#39;-'",
                "&acute;-´",
                "&lt;-<",
                "&gt;->",
                "&laquo;-«",
                "&raquo;-»",
                "&lsaquo;-‹",
                "&rsaquo;-›",
                "&ge;-≥",
                "&le;-≤"
            ];
            for (var i in dec) {
                var decA = dec[i].split("-");
                if (value.indexOf(decA[0]) > -1)
                    value = value.replace(new RegExp(decA[0], "g"), decA[1]);
            }
        }
        return value;
    },
    blobDownload(data, filename) {
        if (window.navigator && window.navigator.msSaveOrOpenBlob) {
            var csvData = new Blob([data], {type: 'text/csv'});
            window.navigator.msSaveOrOpenBlob(csvData, filename);
        } else {
            let url = window.URL.createObjectURL(data)
            let link = document.createElement('a')
            link.style.display = 'none'
            link.href = url
            link.setAttribute('download', filename)
            link.setAttribute('target', "_blank")
            document.body.appendChild(link)
            link.click()
            link.remove();
            window.URL.revokeObjectURL(url);
        }
    },
    isInteger(obj) {
        return Math.floor(obj) === obj;
    },

    replaceXlsxExtension(str = "") {
        if (str.endsWith(".xls") || str.endsWith(".xlsx")) {
            return str;
        }
        return str.replace(/\.\w*/, '.xls');
    },
    toInteger(floatNum) {
        var ret = {times: 1, num: 0};
        if (util.isInteger(floatNum)) {
            ret.num = floatNum;
            return ret;
        }
        var strfi = floatNum + "";
        var dotPos = strfi.indexOf(".");
        var len = strfi.substr(dotPos + 1).length;
        var times = Math.pow(10, len);
        var intNum = parseInt(floatNum * times + 0.5, 10);
        ret.times = times;
        ret.num = intNum;
        return ret;
    },
    operation(a, b, op) {
        var o1 = util.toInteger(a);
        var o2 = util.toInteger(b);
        var n1 = o1.num;
        var n2 = o2.num;
        var t1 = o1.times;
        var t2 = o2.times;
        var max = t1 > t2 ? t1 : t2;
        var result = null;
        switch (op) {
            case "add":
                if (t1 === t2) {
                    // 两个小数位数相同
                    result = n1 + n2;
                } else if (t1 > t2) {
                    // o1 小数位 大于 o2
                    result = n1 + n2 * (t1 / t2);
                } else {
                    // o1 小数位 小于 o2
                    result = n1 * (t2 / t1) + n2;
                }
                return result / max;
            case "subtract":
                if (t1 === t2) {
                    result = n1 - n2;
                } else if (t1 > t2) {
                    result = n1 - n2 * (t1 / t2);
                } else {
                    result = n1 * (t2 / t1) - n2;
                }
                return result / max;
            case "multiply":
                result = (n1 * n2) / (t1 * t2);
                return result;
            case "divide":
                result = (n1 / n2) * (t2 / t1);
                return result;
        }
    },
    add(a, b) {
        //加
        return util.operation(a, b, "add");
    },
    subtract(a, b) {
        //减
        return util.operation(a, b, "subtract");
    },
    multiply(a, b) {
        //乘
        return util.operation(a, b, "multiply");
    },
    divide(a, b) {
        //除
        return util.operation(a, b, "divide");
    },
    closeTabActive(that) {
        that.$parent.$parent.$children[2].closeTabActive();
    },
    closeTab(that, path) {
        that.$parent.$parent.$children[2].closeTab({path});
    },

    // 级联选择器回显，支持多个值
    getNamesFromTreeData(arry, treeData, showLevel) {
        // id(必传) 数组
        // 数据列表(必传)
        // 展示第几层的名称(不必传) start--第一层(默认)  end--最后一层  all--全展示
        var namesArry = [],
            namesStr = "";
        for (var i in arry) {
            if (typeof arry[i] == "object") {
                //多个值 [['123','aaa'],['456','bbb']]
                namesArry.push(this.matchNames(arry[i], treeData, showLevel));
            } else {
                //单个值 ['123','aaa'] 或 "'123','aaa'"
                namesStr = this.matchNames(arry, treeData, showLevel);
            }
        }
        if (namesArry.length > 0) namesStr = namesArry.join("，");
        return namesStr;
    },
    matchNames(value, childrenData, showLevel) {
        var arry = [],
            result = [],
            resultStr = "";
        var depth = 0;
        // 将值转为数组
        if (typeof value == "string") {
            arry = value.indexOf(",") != -1 ? value.split(",") : [value];
        } else {
            arry = value;
        }

        function match(childrenData, depthN) {
            if (childrenData) {
                depth = depthN;
                var flag = 0;
                for (var j in childrenData) {
                    if (arry[depthN] == childrenData[j].id) {
                        flag++;
                        result.push(childrenData[j].name);
                    }

                    if (flag > 0) {
                        if (childrenData[j].children) {
                            depthN++;
                            match(childrenData[j].children, depthN);
                        } else {
                            break;
                        }
                    }
                }
                // 展示层级
                if (showLevel == "end") {
                    resultStr = result[result.length - 1];
                } else if (showLevel == "all") {
                    resultStr = result.join("-");
                } else {
                    resultStr = result[0];
                }
            }
            return resultStr;
        }

        return match(childrenData, depth);
    },
    IsSetupWebOffice(that, fun, data) {
        if (WebOfficeSetup == 0) {
            that.$confirm('系统检测到此机还未安装WebOffice组件！请点击下载安装!', '提示', {
                confirmButtonText: '下载安装包',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                window.open("http://10.92.82.161:8088/uploadFiles/weboffice/WebOffice.rar");
            }).catch(() => {
            });
        } else {
            if (data) {
                fun(data);
            } else {
                fun();
            }
        }
    },
    ShowPage1(params) {
        var varpath = decodeURI(window.location.href);
        var root = varpath.split(router.app._route.path)[0];
        var path = "/fileOnline";
        var strUrl;
        var pre = "WebOffice://|Officectrl|";//智能窗打开的固定参数"WebOffice://|Officectrl.com|";智能窗打开的固定参数,如果将Officectrl.com改成Officectrl则智能窗处理调试模式，显示地址栏内容
        var v = util.getBrowser();
        if (v == 1) {//当浏览器返回值为1时定义为使用智能窗方式打开网址
            strUrl = pre + root + path;
            strUrl = strUrl + "/" + params.id + "/" + params.uid;
            window.open(strUrl, '_self');
        } else { //当浏览器返回值1以外的数据时采用传统方式打开网址
            strUrl = root + path;
            strUrl = strUrl + "/" + params.id + "/" + params.uid;
            window.open(strUrl, '_blank');
        }
    },
    getBrowser() {
        var Sys = {};
        var ua = navigator.userAgent.toLowerCase();
        var s;
        var ver;
        (s = ua.match(/edge\/([\d.]+)/)) ? Sys.edge = s[1] :
            (s = ua.match(/rv:([\d.]+)\) like gecko/)) ? Sys.ie = s[1] :
                (s = ua.match(/msie ([\d.]+)/)) ? Sys.ie = s[1] :
                    (s = ua.match(/firefox\/([\d.]+)/)) ? Sys.firefox = s[1] :
                        (s = ua.match(/chrome\/([\d.]+)/)) ? Sys.chrome = s[1] :
                            (s = ua.match(/opera.([\d.]+)/)) ? Sys.opera = s[1] :
                                (s = ua.match(/version\/([\d.]+).*safari/)) ? Sys.safari = s[1] : 0;
        if (Sys.edge) return 1;
        if (Sys.ie) return 1;
        if (Sys.firefox) return 1;
        if (Sys.chrome) {
            ver = Sys.chrome;
            ver.toLowerCase();
            var arr = ver.split('.');
            if (parseInt(arr[0]) > 43) {
                return 1;
            } else {
                return 0;
            }
        }
        if (Sys.opera) return 1;
        if (Sys.safari) return 1;
        return 1;
    },
    //rsa加密
    getRsa(val, type) {
        var encrypt = new JSEncrypt();
        var key = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC+K3y4fL71dFhFYC9c9bea9wPH" + "\r" +
            "youU86VI0nI1GtDiMbSd3/mFcf/Z14hixordW8W8Q0BftncjcbIOHOeHDK074hpV" + "\r" +
            "bMdJTgadisuksX1fISp5CXa5ETsDcHa6usb1wGd2EFSo8ws5Jfi5oGZVgRzF3YLI" + "\r" +
            "KgxYn+NZu7cvHOD0GwIDAQAB" + "\r";
        encrypt.setPublicKey(key);
        var pk = encrypt.encrypt(val);
        return type ? encodeURIComponent(pk) : encodeURI(pk);//encodeURIComponent
    },
    // 地址拼接
    tourl(url, data) {
        var tourl = url;
        for (var i in data) {
            if (tourl.indexOf('?') > -1)
                tourl += '&' + i + '=' + data[i];
            else
                tourl += '?' + i + '=' + data[i];
        }
        return tourl;
    },
    // 获取地址上的参数
    getQueryString(url) {
        var qs = {};
        var url = url || decodeURIComponent(window.location.href);
        //不管有没有伪静态 都看一下?问号后面的参数
        if (url.indexOf('?') > -1) {
            url = url.substring(url.indexOf('?') + 1);
            var prm = url.split('&');
            for (var p in prm) {
                if (prm[p]) {
                    var sp = prm[p].split('=');
                    if (sp.length > 1) {
                        var spkey = sp[0];
                        var spvalue = (sp[1] == "''" || sp[1] == "\"\"") ? "" : sp[1];

                        if (spvalue.indexOf('#') > -1) {
                            spvalue = spvalue.substring(0, spvalue.indexOf('#'));
                        }
                        qs[spkey] = spvalue;
                    }
                }
            }
        }
        // var reg = new RegExp("(^|&)"+ name +"=([^&]*)(&|$)");
        // var r = window.location.search.substr(1).match(reg);//search,查询？后面的参数，并匹配正则
        // if(r!=null)return  decodeURIComponent(r[2]); return null;     // unescape(r[2]); return null; 针对一般情况
        return qs;
    },

    //流程类型名称和页面（多流程用同一页面：A|C:"请假流程"  A|C:"html/apply/workOrderLeave.html"）
    appNameTH(val) {
        if (typeof appName == "string") {
            return {"type": appName, "path": appHtml};
        } else {
            var hi, ni;
            for (var i in appName) {
                if (i.indexOf("|") > -1) {
                    var iArry = i.split("|");
                    for (var m in iArry) {
                        if (iArry[m] == val) {
                            ni = i;
                            break;
                        }
                    }
                } else {
                    if (i == val) {
                        ni = i;
                        break;
                    }
                    ;
                }
            }
            for (var i in appHtml) {
                if (i.indexOf("|") > -1) {
                    var iArry = i.split("|");
                    for (var m in iArry) {
                        if (iArry[m] == val) {
                            hi = i;
                            break;
                        }
                    }
                } else {
                    if (i == val) {
                        hi = i;
                        break;
                    }
                }
            }
            return {"type": appName[ni], "path": appHtml[hi]};
        }
    },

    // 大写下划线转换驼峰
    toHump(name) {
        var str = name.toLowerCase();
        let arr = [...str];
        arr.forEach((item, index) => {
            if (item === "_") {
                arr.splice(index, 1);
                arr[index] = arr[index].toUpperCase();
            }
        });
        return arr.join('')
    },

    // map数组去重
    newArrFn(arr) {
        let newArr = []
        let map = new Map()
        for (let i = 0; i < arr.length; i++) {
            // 如果 map里面不包含，就设置进去
            if (!map.has(arr[i])) {
                map.set(arr[i], true)
                newArr.push(arr[i])
            }
        }
        ;
        return newArr
    },

    allArr(arr) {
        arr.map((item) => {
            if (Array.isArray(item.list)) {
                util.allArr(item.list)
            } else if (Array.isArray(item.columns)) {
                util.allArr(item.columns)
            } else {
                allnewArr.push(item)
            }
        })
        return allnewArr
    },
    changeAllArr() {
        allnewArr = []
    },
    goToHandelFormList(currentFormList) {
        let returnObj = []
        currentFormList.forEach((item) => {
            let formInfo = {
                class: 'c6',
                label: item.item ? item.item.label : '',
                key: item.name,
                type: item.type,
                placeholder: item.control.placeholder,
                disabled: item.control.readonly,
            }
            // formInfo.class = "c" + Number((item.config.span - 0) / 2).toFixed(0)
            if (item.customRules && item.customRules.length > 0) {
                item.customRules.forEach((element) => {
                    if (element.ruleType == "required") {
                        formInfo.rule = {required: element.required}
                    } else {
                        formInfo.rule = formInfo.rule ? formInfo.rule : {}
                        formInfo.rule.type = element.ruleType
                    }
                });
            } else {
                formInfo.rule = {required: false}
            }

            if (item.control.maxLen) {
                if (formInfo.rule) {
                    formInfo.rule.maxlength = item.control.maxLen
                } else {
                    formInfo.rule = {maxlength: item.control.maxLen}
                }
            }
            switch (item.type) {
                case 'input':
                    formInfo.append = item.config.append
                    formInfo.prepend = item.config.prepend
                    break;
                case 'datePicker':
                    formInfo.type = 'date'
                    formInfo.subtype = item.control.type
                    if (item.control.type == 'year') {
                        formInfo.valueFormat = 'yyyy'
                    } else if (item.control.type == 'month') {
                        formInfo.valueFormat = 'yyyy-MM'
                    } else if (item.control.type == 'date') {
                        formInfo.valueFormat = 'yyyy-MM-dd'
                    } else {
                        formInfo.valueFormat = 'yyyy-MM-dd HH:mm:ss'
                    }
                    break;
                case 'textarea':
                    formInfo.type = 'input'
                    formInfo.inputType = 'textarea'
                    break;
                case 'select':
                case 'radio':
                case 'checkbox' :
                case 'cascader' :
                    if (item.config.type == "async") {
                        formInfo.dictType = item.config.sourceFun
                        formInfo.options = ''
                    } else {
                        formInfo.options = item.options
                    }
                    formInfo.showAllLevels = item.config.levels
                    break;
                case 'treeSelect':
                    if (item.config.type == "async") {
                        formInfo.dictType = item.config.sourceFun
                        formInfo.data = ''
                    } else {
                        formInfo.data = item.control.data
                    }
                    formInfo.multiple = item.control.multiple
                    break;
                case 'downTable':
                    formInfo.dialogData = item.control
                    break;
                case 'group':
                case 'dialog':
                case 'interiorDialog':
                    formInfo.relevancy = item.name + "-" + item.control.stores
                    formInfo.dialogData = item.control
                    formInfo.config = item.config
                    formInfo.readonly = true
                    formInfo.mulitple = item.control.multiple
                    break;
                case 'echoTable':
                    formInfo.tableInfo = item.control
                    formInfo.label = ''
                    break;
                case 'uploadFile':
                    formInfo.type = 'sbUpload'
                    formInfo.data = {}
                    formInfo.fileId = item.control.fileId
                    formInfo.multiple = item.control.multiple
                    formInfo.limit = item.control.multipleNum
                    formInfo.btnText = item.config.btnText
                    formInfo.listType = item.config.fileType ? (item.config.fileType).join(",") : ''
                    break;
                case 'switch':
                    formInfo.defaultValue = false
                    break;
                case 'sublist':
                    // 1弹窗 2行内编辑
                    formInfo.key = item.control.childId + 'List'
                    formInfo.tableInfo = item
                    formInfo.label = ""
                    break;
                case 'org':
                case 'department':
                    formInfo.type = 'org'
                    formInfo.typeInfo = item.type
                    formInfo.readonly = true
                    formInfo.relevancy = formInfo.key + "-" + item.control.stores
                    formInfo.nodeKey = "orgCode"
                    formInfo.mulitple = item.control.multiple || false
                    formInfo.stepLoad = true
                    formInfo.appendShow = item.config.appendShow
                    formInfo.btnText = item.config.append
                    formInfo.defaultProps = {
                        children: "children",
                        label: "orgName"
                    }
                    break;
                case 'company':
                    formInfo.readonly = true
                    formInfo.relevancy = formInfo.key + "-" + item.control.stores
                    formInfo.nodeKey = "orgCode"
                    formInfo.mulitple = item.control.multiple || false
                    formInfo.stepLoad = true
                    formInfo.appendShow = item.config.appendShow
                    formInfo.btnText = item.config.append
                    formInfo.defaultProps = {
                        children: "children",
                        label: "orgName"
                    }
                    break;
                case 'user':
                    formInfo.readonly = true
                    formInfo.relevancy = formInfo.key + "-" + item.control.stores
                    formInfo.nodeKey = "id"
                    formInfo.mulitple = item.control.multiple || false
                    formInfo.stepLoad = true
                    formInfo.appendShow = item.config.appendShow
                    formInfo.btnText = item.config.append
                    formInfo.defaultProps = {
                        children: "children",
                        label: "name",
                        isLeaf: 'leaf',
                    }
                    break;
                case 'schedule':
                    formInfo.control = {
                        findAll: item.control.findAll,
                        create: item.control.create,
                        update: item.control.update,
                        delete: item.control.delete
                    }
                    break;
                case 'txt':
                case 'link':
                case 'divider':
                    formInfo.item = item.item
                    formInfo.control = item.control
                    formInfo.label = ""
                    break;
                case 'tip':
                    let info = {}
                    info = {class: "c12", type: "tip", value: item.control.modelValue, show: formInfo.show, key: item.name,}
                    formInfo = info
                    break;
                case 'association':
                    formInfo.association = item.control.association
                    formInfo.associationId = item.control.associationId
                    formInfo.associationOptions = item.control.associationOptions
                    formInfo.readonly = true
                    delete formInfo.disabled
                    break;
                case 'card':
                    formInfo.header = formInfo.label
                    formInfo.list = item.list
                    formInfo.label = ""
                    break;
                case 'collapse':
                    formInfo.header = formInfo.label
                    formInfo.columns = item.columns
                    formInfo.control = item.control
                    formInfo.label = ""
                    break;
                case 'slider':
                    formInfo.min = item.control.min - 0
                    formInfo.max = item.control.max - 0
                    formInfo.step = item.control.step - 0
                    break;
                case 'timestatus':
                    formInfo.findAll = item.control.findAll
                    formInfo.label = ""
                    break;
                case 'score':
                    formInfo.allScore = 0
                    formInfo.suggest = ''
                    formInfo.selectIds = ''
                    formInfo.findAll = item.control.findAll
                    formInfo.label = ""
                    break;
                default:
                    break;
            }
            Object.keys(formInfo).forEach(key => {
                if (String(formInfo[key]) == 'undefined') {
                    delete formInfo[key]
                }
            })
            returnObj.push(formInfo)
        })
        // console.log('returnObj', returnObj)
        return returnObj
    },
    changeListInfo(formInfo, item) {
        formInfo.class = 'c' + Number((item.config.span - 0) / 2).toFixed(0)
        if (item.customRules && item.customRules.length > 0) {
            item.customRules.forEach((element) => {
                if (element.ruleType == "required") {
                    formInfo.rule = {required: element.required}
                } else {
                    formInfo.rule = formInfo.rule ? formInfo.rule : {}
                    formInfo.rule.type = element.ruleType
                }
            });
        } else {
            formInfo.rule = {required: false}
        }

        if (item.control.maxLen) {
            if (formInfo.rule) {
                formInfo.rule.maxlength = item.control.maxLen
            } else {
                formInfo.rule = {maxlength: item.control.maxLen}
            }
        }
        switch (item.type) {
            case 'input':
                formInfo.append = item.config.append
                formInfo.prepend = item.config.prepend
                break;
            case 'datePicker':
                formInfo.type = 'date'
                formInfo.subtype = item.control.type
                if (item.control.type == 'year') {
                    formInfo.valueFormat = 'yyyy'
                } else if (item.control.type == 'month') {
                    formInfo.valueFormat = 'yyyy-MM'
                } else if (item.control.type == 'date') {
                    formInfo.valueFormat = 'yyyy-MM-dd'
                } else {
                    formInfo.valueFormat = 'yyyy-MM-dd HH:mm:ss'
                }
                break;
            case 'textarea':
                formInfo.type = 'input'
                formInfo.inputType = 'textarea'
                break;
            case 'select':
            case 'radio':
            case 'checkbox' :
            case 'cascader' :
                if (item.config.type == "async") {
                    formInfo.dictType = item.config.sourceFun
                    formInfo.options = ''
                } else {
                    formInfo.options = item.options
                }
                formInfo.showAllLevels = item.config.levels
                break;
            case 'treeSelect':
                if (item.config.type == "async") {
                    formInfo.dictType = item.config.sourceFun
                    formInfo.data = ''
                } else {
                    formInfo.data = item.control.data
                }
                formInfo.multiple = item.control.multiple
                break;
            case 'downTable':
                formInfo.dialogData = item.control
                break;
            case 'group':
            case 'dialog':
            case 'interiorDialog':
                formInfo.relevancy = item.name + "-" + item.control.stores
                formInfo.dialogData = item.control
                formInfo.config = item.config
                formInfo.readonly = true
                formInfo.mulitple = item.control.multiple
                break;
            case 'echoTable':
                formInfo.tableInfo = item.control
                formInfo.label = ''
                break;
            case 'uploadFile':
                formInfo.type = 'sbUpload'
                formInfo.data = {}
                formInfo.fileId = item.control.fileId
                formInfo.multiple = item.control.multiple
                formInfo.limit = item.control.multipleNum
                formInfo.btnText = item.config.btnText
                formInfo.listType = item.config.fileType ? (item.config.fileType).join(",") : ''
                break;
            case 'switch':
                formInfo.defaultValue = false
                break;
            case 'sublist':
                // 1弹窗 2行内编辑
                formInfo.key = item.control.childId + 'List'
                formInfo.tableInfo = item
                formInfo.label = ""
                break;
            case 'org':
            case 'department':
                formInfo.type = 'org'
                formInfo.typeInfo = item.type
                formInfo.readonly = true
                formInfo.relevancy = formInfo.key + "-" + item.control.stores
                formInfo.nodeKey = "orgCode"
                formInfo.mulitple = item.control.multiple || false
                formInfo.stepLoad = true
                formInfo.appendShow = item.config.appendShow
                formInfo.btnText = item.config.append
                formInfo.defaultProps = {
                    children: "children",
                    label: "orgName"
                }
                break;
            case 'company':
                formInfo.readonly = true
                formInfo.relevancy = formInfo.key + "-" + item.control.stores
                formInfo.nodeKey = "orgCode"
                formInfo.mulitple = item.control.multiple || false
                formInfo.stepLoad = true
                formInfo.appendShow = item.config.appendShow
                formInfo.btnText = item.config.append
                formInfo.defaultProps = {
                    children: "children",
                    label: "orgName"
                }
                break;
            case 'user':
                formInfo.readonly = true
                formInfo.relevancy = formInfo.key + "-" + item.control.stores
                formInfo.nodeKey = "id"
                formInfo.mulitple = item.control.multiple || false
                formInfo.stepLoad = true
                formInfo.appendShow = item.config.appendShow
                formInfo.btnText = item.config.append
                formInfo.defaultProps = {
                    children: "children",
                    label: "name",
                    isLeaf: 'leaf',
                }
                break;
            case 'schedule':
                formInfo.control = {
                    findAll: item.control.findAll,
                    create: item.control.create,
                    update: item.control.update,
                    delete: item.control.delete
                }
                break;
            case 'txt':
            case 'link':
            case 'divider':
                formInfo.item = item.item
                formInfo.control = item.control
                formInfo.label = ""
                break;
            case 'tip':
                let info = {}
                info = {class: "c12", type: "tip", value: item.control.modelValue, show: formInfo.show, key: item.name,}
                formInfo = info
                break;
            case 'association':
                formInfo.association = item.control.association
                formInfo.associationId = item.control.associationId
                formInfo.associationOptions = item.control.associationOptions
                formInfo.readonly = true
                delete formInfo.disabled
                break;
            case 'card':
                formInfo.header = formInfo.label
                formInfo.list = item.list
                formInfo.label = ""
                break;
            case 'collapse':
                formInfo.header = formInfo.label
                formInfo.columns = item.columns
                formInfo.control = item.control
                formInfo.label = ""
                break;
            case 'slider':
                formInfo.min = item.control.min - 0
                formInfo.max = item.control.max - 0
                formInfo.step = item.control.step - 0
                break;
            case 'timestatus':
                formInfo.findAll = item.control.findAll
                formInfo.label = ""
                break;
            case 'score':
                formInfo.allScore = 0
                formInfo.suggest = ''
                formInfo.selectIds = ''
                formInfo.findAll = item.control.findAll
                formInfo.label = ""
                break;
            default:
                break;
        }
        return formInfo
    },
    // 校验吨数，费用输入框  保留两位小数
    checkInput(value) {
        value = value.replace(/[^\d.]/g, '')
        value = value.replace(/^\./g, '')
        value = value.replace(/^0{2,}/g, '0')
        value = value.replace(/\.{2,}/g, '.')
        // value = value.replace(/\.{1,}/g, '')
        value = value.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.')
        value = value.replace('-', '$#$').replace(/\-/g, '').replace('$#$', '-')
        value = value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3')
        // value = value.replace(/^(\-)*(\d+)\.(\d).*$/, '$1$2')
        if ((value.charAt(0) == 0) && (parseInt(value) >= 1)) {
            return value.substring(1, value.length)
        } else {
            return value
        }
    },
    // 校验结束时间是否在开始时间之前
    checkTime(start, end) {
        let s_stamp = new Date(start.replace(/-/g, '/')).getTime()
        let e_stamp = new Date(end.replace(/-/g, '/')).getTime()
        return s_stamp < e_stamp
    },
    // table 表头行固定 左右列固定
    stickyThead(el, binging, vnode) {
        let top = "0px";
        //如果是吸顶高度
        if (!isNaN(Number(binging.value))) {
            top = binging.value + "px";
        }
        //如果是选择器名
        if (typeof binging.value === "string") {
            let rect = document.querySelector(binging.value)?.getBoundingClientRect();
            top = rect ? rect.top + rect.height + "px" : "0px";
        }
        el.style.overflow = "visible";
        const tHeader = el.querySelector(".el-table__header-wrapper");
        tHeader.style.position = "sticky";
        tHeader.style.top = top;
        tHeader.style.zIndex = 10;

        const ths = tHeader.querySelectorAll("th.is-hidden");
        ths.forEach((item) => {
            item.classList.remove("is-hidden");
        });


        // 找到实例
        const table = vnode.context.$children.find((item) => item.$el === el);
        // 找到左边固定列的信息
        const leftFixed = table.fixedColumns;
        if (leftFixed && leftFixed.length) {
            let leftFixedWidth = 0;
            leftFixed.forEach((item) => {
                let itemW = item.width || item.realWidth || item.minWidth;
                const cell = tHeader.querySelector("th." + item.id);
                if (cell) {
                    cell.style.position = "sticky";
                    cell.style.left = leftFixedWidth + "px";
                    cell.style.top = top;
                    cell.style.zIndex = 10;
                    leftFixedWidth += itemW;
                }
            });
        }

        // 找到右边固定列
        const rightFixed = table.rightFixedColumns;
        if (rightFixed && rightFixed.length) {
            let rightFixedWidth = 0;
            for (let i = rightFixed.length - 1; i >= 0; i--) {
                let itemW = rightFixed[i].width || rightFixed[i].realWidth || rightFixed[i].minWidth;
                const cell = tHeader.querySelector("th." + rightFixed[i].id);
                if (cell) {
                    cell.style.position = "sticky";
                    cell.style.right = rightFixedWidth + "px";
                    cell.style.top = top;
                    cell.style.zIndex = 10;
                    rightFixedWidth += itemW;
                }
            }
        }
    }
};


export default util;
