import request from "@/assets/js/request";
import store from "../store";
import util from "@/assets/js/public";

// 获取决策项
export function getDecision(params, pmInsId) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/process/findDecisions?source=PC&pmInsId=${pmInsId}`,
        contentType: "application/json; charset=utf-8",
        data: params?params:{}
    });
}
// 获取版本号
export function getLastVersion(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/process/getLastVersionByProDefId?key=${params.key}&tenantId=${params.tenantId}`,
        contentType: "application/json; charset=utf-8"
    });
}
// 获取人员组织树
export function getOrgAndUser(params, pmInsId) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/common/getOrgAndUser?source=PC&pmInsId=${pmInsId}`,
        contentType: "application/json; charset=utf-8",
        data: params?params:{}
    });
}

// 我的待办
export function findProcessTask(params){
    let data = params?params:{};
    data.databaseType = "oracle";
    data.participant = "OA";
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/task/myTaskToDo?source=PC&page=${params.page}&rows=${params.size}&loginuser=${store.state.user.user.username}`,
        contentType: "application/json;charset=UTF-8",
		data: data
    });
}

// 我的草稿
export function findProcessDraft(params){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/common/myDraftToDo?source=PC&page=${params.page}&size=${params.size}&loginuser=${store.state.user.user.username}`,
        contentType: "application/json;charset=UTF-8",
		data: params
    });
}

// 我的已办
export function findProcessJoin(params){
    let data = params?params:{};
    data.databaseType = "oracle";
    data.participant = "OA";
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/task/myJoin?source=PC&page=${params.page}&rows=${params.size}&loginuser=${store.state.user.user.username}`,
        contentType: "application/json;charset=UTF-8",
		data: data
    });
}

// 待阅列表
export function findProcessRead(params){
    let data = params?params:{};
    data.databaseType = "oracle";
    data.participant = "OA";
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/process/findMyPending?source=PC&page=${params.page}&size=${params.size}&loginuser=${store.state.user.user.username}`,
        contentType: "application/json;charset=UTF-8",
		data: data
    });
}

// 已阅列表
export function findProcessDoRead(params){
    let data = params?params:{};
    data.databaseType = "oracle";
    data.participant = "OA";
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/process/findMyRead?source=PC&page=${params.page}&size=${params.size}&loginuser=${store.state.user.user.username}`,
        contentType: "application/json;charset=UTF-8",
		data: data
    });
}

// 流程跟踪
export function findFlowTracking(pmInsId){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/task/findTaskOptMsg?pmInsId=${pmInsId}&source=PC`,
        contentType: "application/json;charset=UTF-8"
    });
}

// 流程图
export function getDiagram(processInstanceId){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/process/getDiagram?processInstanceId=${processInstanceId}`,
        method:'get',
        contentType: "application/json;charset=UTF-8",
        responseType: "blob"
    });
}

// 查看意见
export function getWfOptMags(processInstanceId){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/process/getWfOptMags?processInstanceId=${processInstanceId}`,
        contentType: "application/json;charset=UTF-8"
    });
}

// 废除草稿
export function deleteDraft(params){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/common/deleteDraft`,
        contentType: "application/json;charset=UTF-8",
        data: params
    });
}


// 查询待阅的流程跟踪和意见
export function flowTodoReTracking(processInstanceId){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/process/flowTodoReTracking?processInstanceId=${processInstanceId}`,
        contentType: "application/json;charset=UTF-8"
    });
}

// 获取管理员
export function adminlist(params, data){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/managerUserInfo/findListByPage?page=${params.page}&size=${params.size}`,
        contentType: "application/json;charset=UTF-8",
        data
    });
}
// 新增管理员
export function adminadd(data){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/managerUserInfo/create`,
        contentType: "application/json;charset=UTF-8",
        data
    });
}
// 修改管理员
export function adminupdate(data){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/managerUserInfo/update`,
        contentType: "application/json;charset=UTF-8",
        data
    });
}
// 删除管理员
export function adminremove(id){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/managerUserInfo/deleteById?id=${id}`,
        contentType: "application/json;charset=UTF-8",
    });
}
// 导出管理员
export function adminexport(data){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/managerUserInfo/export`,
        contentType: "application/json;charset=UTF-8",
        data,
        responseType: "blob"
    });
}
// 获取当前人信息
export function getuserinfo(applyNumber){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/baseInfo/getCurrentUserInfo?applyNumber=${applyNumber}`,
        contentType: "application/json;charset=UTF-8"
    });
}
// 修改当前人信息
export function updateuserinfo(data){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/baseInfo/update`,
        contentType: "application/json;charset=UTF-8",
        data
    });
}

// 预览
export function preview(params, data){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/common/preview?taskId=${params.taskId}&location=${params.location}&pmInsId=${params.pmInsId}`,
        contentType: "application/json;charset=UTF-8",
        data
    });
}
// 流程提交
export function processNext(params, data){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/common/startSubmitProcess?taskId=${params.taskId}&location=${params.location}&pmInsId=${params.pmInsId}`,
        contentType: "application/json;charset=UTF-8",
        data
    });
}
// 汇总导出
export function totalexport(data){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/common/totalDetailExport`,
        contentType: "application/json;charset=UTF-8",
        data,
        responseType: "blob"
    });
}
// 汇总导出pdf
export function totalDetailExportPDF(data){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/common/totalDetailExportPDF`,
        contentType: "application/json;charset=UTF-8",
        data,
        responseType: "blob"
    });
}
// 汇总查询
export function totalquery(params){
    let data = params ? params : {}
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/common/getTotalDetail?page=${params.page}&size=${params.size}`,
        contentType: "application/json;charset=UTF-8",
        data
    });
}
// 汇总查询催办数量
export function cuibanquery(applyNumber){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/common/getReSendUserConut?applyNumber=${applyNumber}`,
        contentType: "application/json;charset=UTF-8"
    });
}
// 汇总催办
export function cuiban(applyNumber){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/common/sendShortMessage?applyNumber=${applyNumber}`,
        contentType: "application/json;charset=UTF-8"
    });
}

// 汇总导出
export function writeexport(data){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/common/saveDetailExport`,
        contentType: "application/json;charset=UTF-8",
        data,
        responseType: "blob"
    });
}
// 汇总查询
export function writequery(params){
    let data = params ? params : {}
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/common/getSaveDetail?page=${params.page}&size=${params.size}`,
        contentType: "application/json;charset=UTF-8",
        data
    });
}
// 操作说明地址
export function operatetip(){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/common/getFileDict`,
        contentType: "application/json;charset=UTF-8"
    });
}
// 年份字典值
export function getYearDict(dictType, isPublic) {
	return request({
		url: `/${process.env.VUE_APP_APPCODE}/action/common/getYearDict`,
		contentType: "application/json;charset=UTF-8",
		
	});
}
// 亲属导出
export function baseExportQuery(data){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/usChild/exportQuery`,
        contentType: "application/json;charset=UTF-8",
        data,
        responseType: "blob"
    });
}
// 汇总查询
export function baseGetQueryPage(params){
    let data = params ? params : {}
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/usChild/getQueryPage?page=${params.page}&size=${params.size}`,
        contentType: "application/json;charset=UTF-8",
        data
    });
}